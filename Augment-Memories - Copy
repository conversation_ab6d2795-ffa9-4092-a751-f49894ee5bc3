



















































# TeERA BOT Project
- TeERA BOT project uses Telegram bot token **********************************************.
- TeERA bot new version is working successfully and responding to /start commands in Telegram with proper Arabic text formatting.
- TeERA bot should have branch selection (1-التجمع, 2-مدينة نصر, 3-للكل), date range input (or # for all), and generate reports for customers by regions and social media channels.
- For TeERA bot customer inquiries, user wants employee and customer codes/details shown alongside customer data, not just customer counts.
- User prefers customer inquiry results to show dates as headers with customers listed underneath, rather than showing individual dates next to each customer, and wants proper chronological ordering.
- User prefers customer inquiry results to be ordered from oldest to newest by customer addition order (1, 2, 3...).
- User prefers TeERA bot customer inquiry results to show 100 customers per page in simplified format: sequential number, customer name, with date grouping (e.g., '139 - م/ ابراهيم محمد السيد').
- User prefers to remove 🆔 emoji from TeERA bot customer display because it's an English symbol that makes messages too long.
- TeERA bot should use database connection: SERVER=alisamaraa.ddns.net,4100; DATABASE=Terra; UID=sa; PWD=@a123admin4
- User wants the /test command to display server name and database name information.
- User prefers 3 seconds delay between TeERA BOT messages and wants a success confirmation message at the end saying the query was completed successfully.
- User prefers that after displaying customer search results, the bot should ask to search for another customer or provide option to return to main menu.
- User prefers that after displaying any report results, the bot should offer options to search again or return to main menu with clear action buttons.
- User prefers that after any operation (successful or failed), the bot should show options to enter a new time period or return to the main menu with /start.
- User prefers that when no data is found for a specified time period in reports, the bot should offer options to enter a new time period or return to the main menu instead of ending the interaction.
- User prefers that when incorrect search input is provided, the bot should prompt to re-enter correct information rather than ending the interaction.
- TeERA bot needs customer report features for customers at different workflow stages: customers without previews, customers with previews but no meetings, customers with meetings but no designs, and customers with designs but no contracts, using sys_preview table and following the same branch/date selection pattern.
- TeERA bot needs to show previews categorized by status: active/running previews, finished previews, and all previews combined.
- User prefers that all reports should always display the total count/number at the end of the report.
- User prefers that long reports should be split into multiple messages to avoid Telegram's message length limit when running bots in groups.
- User prefers TeERA bot reports to always show counts/statistics first, then offer a second message asking if user wants detailed customer information with their data.
- User prefers TeERA bot report names: 'عملاء بدون معاينات', 'معاينات بدون اجتماعات', 'اجتماعات بدون تصميمات', 'تصميمات بدون عقود' and wants two-stage system: statistics first, then optional detailed customer data.
- User wants PDF export functionality for reports with nice and organized Arabic formatting.
- User prefers that the 'عرض التفاصيل' (show details) button should directly export a detailed PDF report instead of showing text details first.
- User prefers Arabic customer names to be displayed exactly as stored in the database in PDF reports, not converted to codes or English.
- User prefers TeERA bot PDF reports to include detailed numerical statistics (active customers, inactive customers, customers with/without previews), visible PDF titles, and display all customers instead of limiting to 100.
- User wants detailed customer and preview statistics breakdown including: total customers, deleted customers, customers with previews who are deleted, total previews, deleted previews, active previews, converted vs non-converted previews, with reference to PreviewConvert field in Sys_Previews table.
- User prefers TeERA bot PDF reports to display customer status next to each customer name in addition to other customer details.
- TeERA BOT database table names: previews (Sys_Previews, Sys_PreviewTypes), designs (Sys_Designs), contracts (Acc_ContractCategories, Acc_Contracts), meetings (Sys_Meetings).
- TeERA BOT database table columns: Sys_Previews uses CustomerId/Date, Sys_Designs uses CustomerId/Date, Acc_Contracts uses CustomerId/Date, Sys_Meetings uses CustomerId/Date.
- TeERA BOT database schema: Sys_Previews has CustomerId/MeetingId columns, Sys_Designs has MeetingId/ContractId columns, Acc_Contracts has DesignId column, and Sys_Meetings has PreviewId/DesignId columns for linking workflow stages.
- TeERA BOT database schema details: Sys_Previews (PreviewId, BranchId, CustomerId, PreviewStatus, PreviewConvert, IsDeleted, etc.), Sys_Designs (DesignId, BranchId, MeetingId, CustomerId, ContractId, IsDeleted, etc.), Acc_Contracts (ContractId, BranchId, DesignId, CustomerId, IsDeleted, etc.), and Sys_Meetings (MeetingId, BranchId, PreviewId, CustomerId, DesignId, IsDeleted, etc.) with workflow relationships between tables.
- User wants a new query feature in TeERA bot to find customers who have more than one preview and show the count of their previews.

# User Preferences
- User prefers bots to run continuously in terminal without disconnecting, staying active for /start commands.
- User prefers to continue working on existing bot files and modify them rather than creating new separate versions that remove previous functionality.
- User prefers to create new bots from scratch rather than fixing existing problematic code.
- User prefers to run Python bots using 'play' button from the same screen/interface rather than separate run commands to avoid path specification prompts.
- User prefers to run Python scripts using the full path command: "C:/Users/<USER>/AppData/Local/Programs/Python/Python313/python.exe" instead of just 'python'.

# Python Environment
- User has Python 3.13.3 installed with multiple Python paths including C:\Users\<USER>\AppData\Local\Programs/Python/Python313/python.exe as the main installation.
- User prefers VS Code to be properly configured for Python development and wants it fixed when there are environment issues.