# بوت TeERA - نظام إدارة قاعدة البيانات

## نظرة عامة
بوت تليجرام متقدم للتعامل مع قاعدة بيانات Terra، يوفر واجهة سهلة الاستخدام للبحث عن العملاء والموظفين وإدارة الملفات.

## الميزات الرئيسية

### 🔍 البحث والاستعلامات
- **بحث العملاء**: البحث بكود العميل أو رقم الهاتف
- **بحث الموظفين**: البحث عن بيانات الموظفين
- **استعلامات إحصائية**: عدد العملاء حسب المناطق ووسائل التواصل

### 📁 إدارة الملفات
- **تحميل معاينات العملاء**: تحميل ملفات المعاينات
- **تحميل اجتماعات**: تحميل ملفات الاجتماعات
- **تحميل ملفات الموظفين**: إدارة ملفات الموظفين

### 🛠️ الأدوات المساعدة
- **اختبار الاتصال**: التحقق من حالة قاعدة البيانات
- **نظام المساعدة**: دليل شامل للاستخدام
- **تسجيل الأخطاء**: نظام متقدم لتتبع الأخطاء

## متطلبات التشغيل

### المكتبات المطلوبة
```bash
pip install python-telegram-bot==20.7
pip install cryptography==41.0.7
pip install pyodbc==4.0.39
```

### ملفات الإعداد المطلوبة

#### 1. ملف `db_credentials.txt`
```
SERVER=hej08pxktvw.sn.mynetname.net
USER=sa
PASSWORD=Ret_ME@
```

#### 2. ملف `CREDINTEL.txt`
```
token:7556046481:AAEMttgLYzrMu1q2ogRlgsV8mgscqZKbbcY
SERVER=hej08pxktvw.sn.mynetname.net
USER=sa
PASSWORD=Ret_ME@
```

## طرق التشغيل

### 1. التشغيل البسيط (للاختبار)
```bash
python test_bot.py
```

### 2. التشغيل المتقدم
```bash
python terra_bot_enhanced.py
```

### 3. التشغيل الأصلي
```bash
python "boot finish33 - Copy.py"
```

## الأوامر المتاحة

### أوامر البوت
- `/start` - بدء البوت وعرض القائمة الرئيسية
- `/test` - اختبار الاتصال بقاعدة البيانات
- `/help` - عرض المساعدة

### الوظائف التفاعلية
- **بحث عميل** - البحث عن العملاء بالكود أو الهاتف
- **بحث موظف** - البحث عن الموظفين
- **استعلام عن العملاء** - عرض قوائم العملاء حسب الفرع
- **عدد العملاء حسب المناطق** - إحصائيات المناطق
- **عدد عملاء وسائل التواصل** - إحصائيات وسائل التواصل
- **تحميل معاينات** - تحميل ملفات المعاينات
- **تحميل اجتماعات** - تحميل ملفات الاجتماعات
- **تحميل ملفات موظف** - تحميل ملفات الموظفين

## هيكل المشروع

```
TeERA BOT/
├── boot finish33 - Copy.py    # البوت الأصلي
├── terra_bot_enhanced.py      # النسخة المحسنة
├── test_bot.py               # نسخة الاختبار
├── run_bot.py               # ملف التشغيل
├── db_credentials.txt       # بيانات قاعدة البيانات
├── CREDINTEL.txt           # ملف الإعدادات
├── requirements.txt        # المكتبات المطلوبة
└── README.md              # هذا الملف
```

## الأمان والحماية

### تشفير التوكن
البوت يدعم تشفير توكن التليجرام لحماية إضافية:

```python
# إنشاء ملفات التشفير
python create_encrypted_token.py
```

### حماية قاعدة البيانات
- اتصال آمن بقاعدة البيانات
- التحقق من صحة البيانات
- منع SQL Injection

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. خطأ في الاتصال بقاعدة البيانات
```
❌ تعذر الاتصال بقاعدة البيانات
```
**الحل**: تحقق من:
- صحة بيانات الاتصال في `db_credentials.txt`
- أن الخادم متاح ويقبل الاتصالات
- أن SQL Server Driver مثبت

#### 2. خطأ في توكن البوت
```
❌ لم يتم العثور على توكن البوت
```
**الحل**: تأكد من وجود التوكن في `CREDINTEL.txt`

#### 3. مشاكل المكتبات
```
❌ خطأ في استيراد المكتبات
```
**الحل**: 
```bash
pip install -r requirements.txt
```

## التطوير والتحسين

### إضافة وظائف جديدة
1. أضف الدالة في الملف المناسب
2. أضف المعالج في `main()`
3. اختبر الوظيفة

### تحسين الأداء
- استخدام Connection Pooling
- تحسين الاستعلامات
- إضافة Cache للبيانات المتكررة

## الدعم الفني

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
- تحقق من ملف `terra_bot.log` للأخطاء
- راجع هذا الدليل للحلول الشائعة
- اتصل بفريق التطوير

## الترخيص

هذا المشروع مخصص للاستخدام الداخلي لشركة Terra.

---

**آخر تحديث**: ديسمبر 2024
**الإصدار**: 1.0
