#الملفات فى ده تمام 
#استعلام حسب المناطق   1 - 2 مظبوط
#انهاء المحادثه بعد كل امر بس بيبدء من جديد 

import os
import pyodbc
import asyncio
from cryptography.fernet import Fernet
from telegram import Update, ReplyKeyboardMarkup, KeyboardButton
from telegram import <PERSON>t<PERSON><PERSON><PERSON>


from telegram import ReplyKeyboardRemove


from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes, ConversationHandler
import logging

DOWNLOAD_FILES, SELECT_BRANCH, SELECT_DAYS, SEARCH_CUSTOMER, SEARCH_EMPLOYEES, GENERAL_INQUIRY = range(6)

BRANCHES = {"1": "التجمع", "2": "مدينة نصر", "/START": "مدينة نصر"}
LOGIN_USERNAME, LOGIN_PASSWORD = range(2)


import logging

logging.basicConfig(
    filename="bot_errors.log",
    level=logging.ERROR,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

def decrypt_token():
    try:
        # محاولة قراءة التوكن المشفر
        with open('encryption_key.key', 'rb') as key_file:
            key = key_file.read()
        fernet = Fernet(key)

        with open('encrypted_token.txt', 'rb') as token_file:
            encrypted_token = token_file.read()

        decrypted_token = fernet.decrypt(encrypted_token).decode()
        return decrypted_token
    except Exception as e:
        print(f"خطأ في فك التشفير: {e}")
        # في حالة فشل فك التشفير، استخدم التوكن مباشرة من ملف CREDINTEL.txt
        try:
            with open('CREDINTEL.txt', 'r') as file:
                for line in file:
                    if line.startswith('token:'):
                        return line.split(':', 1)[1].strip()
        except Exception as e2:
            print(f"خطأ في قراءة التوكن: {e2}")
        return None

def read_credentials():
    credentials = {}
    with open('db_credentials.txt', 'r') as file:
        for line in file:
            key, value = line.strip().split('=')
            credentials[key] = value
    return credentials

def connect_to_db():
    credentials = read_credentials()
    server = credentials.get('SERVER', '') 
    database = 'Terra'
    user = credentials.get('USER')
    password = credentials.get('PASSWORD')
    
    if user == '0' and password == '0':
        conn = pyodbc.connect(f'DRIVER={{SQL Server}};SERVER={server};DATABASE={database};Trusted_Connection=yes')
    else:
        conn = pyodbc.connect(f'DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={user};PWD={password}')
    
    return conn

async def set_bot_commands(application):
    commands = [
        BotCommand("start", "بدء البوت"),
        BotCommand("run", "تشغيل البوت"),
        BotCommand("re_load", "إيقاف البوت"),
        BotCommand("Done", "الأمر الخاص"),
    ]
    await application.bot.set_my_commands(commands)


async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
     
    keyboard = [
    [KeyboardButton("بحث عميل"), KeyboardButton("استعلام عن العملاء")],
    [KeyboardButton("عدد عملاء وسائل التواصل"), KeyboardButton("عدد العملاء حسب المناطق")],
    [KeyboardButton("بحث موظف"), KeyboardButton("تحميل ملفات موظف")],
    [KeyboardButton("تحميل معاينات"), KeyboardButton("تحميل اجتماعات")],
]




    reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=False)
    
    await update.message.reply_text("مرحباً! اختر أحد الخيارات التالية:", reply_markup=reply_markup)


#جزء المعاينة ---------------  جزء المعاينة   ----------جزء المعاينة-------------    جزء المعاينة--------------جزء المعاينة----
#زرار البدايه السش



async def custom_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    await update.message.reply_text("تم تنفيذ الأمر الخاص!")





# -------------------------------------------------------
async def list_preview_contents(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    base_path = r"D:\File Terra\PublishAPI\Uploads\preview"
    if os.path.exists(base_path):
        folders = os.listdir(base_path)
        contents = []

        for folder in folders:
            folder_path = os.path.join(base_path, folder)
            if os.path.isdir(folder_path):
                files = os.listdir(folder_path)
                contents.append(f"📁 {folder}:\n" + "\n".join([f"  - {file}" for file in files]))

        if contents:
            message = "\n\n".join(contents)
            await update.message.reply_text(f"📂 محتويات Preview:\n\n{message}")
        else:
            await update.message.reply_text("📂 لا توجد فولدرات أو ملفات في Preview.")
    else:
        await update.message.reply_text("📂 مسار Preview غير موجود على السيرفر.")








#-------------------------------------------------داله البداية --------------------------------------------------------------
async def download_previews_start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    await update.message.reply_text("من فضلك أدخل كود العميل لتحميل معايناته (مثل: ):")
    return DOWNLOAD_FILES



#-------------------------------------------------داله الزرار --------------------------------------------------------------


async def download_previews(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """
    تحميل الملفات المرتبطة بكود العميل، مع عرض قائمة الملفات قبل التحميل.
    """
    client_code = update.message.text.strip()

    if client_code == "إنهاء المحادثة":
        await cancel(update, context)  # استدعاء دالة الإنهاء
        return ConversationHandler.END  # إنهاء المحادثة     

    if not client_code:
        await update.message.reply_text("⚠️ كود العميل المدخل فارغ. من فضلك أدخل كود العميل.")
        return DOWNLOAD_FILES

    try:
        # الاتصال بقاعدة البيانات
        conn = connect_to_db()
        cursor = conn.cursor()
        query = """
        SELECT Sys_Files.FileUrl, Sys_Files.FileSize, Sys_Files.AddDate
        FROM Acc_Customers
        INNER JOIN Sys_Previews ON Acc_Customers.CustomerId = Sys_Previews.CustomerId
        INNER JOIN Sys_Files ON Sys_Previews.PreviewId = Sys_Files.PreviewId
        WHERE Acc_Customers.CustomerCode = ? AND Sys_Files.IsDeleted = 0
        ORDER BY Sys_Files.AddDate ASC
        """
        cursor.execute(query, (client_code,))
        files = cursor.fetchall()
        conn.close()

        if files:
            # عرض قائمة بأسماء الملفات قبل البدء في التحميل
            file_names = [os.path.basename(file[0]) for file in files]
            file_list_message = "📋 الملفات التي سيتم تحميلها:\n" + "\n".join(file_names)
            await update.message.reply_text(file_list_message)

            for file in files:
                # التحقق إذا كان المستخدم أرسل "إنهاء المحادثة"
                if context.user_data.get("cancel_requested"):
                    await cancel(update, context)
                    return ConversationHandler.END

                raw_file_path, file_size, add_date = file
                corrected_file_path = raw_file_path.replace("/Uploads", "D:\\File Terra\\PublishAPI\\Uploads").replace("/", "\\")

                if os.path.exists(corrected_file_path):
                    if file_size > 0:
                        # إرسال رسالة تفيد ببدء تحميل الملف
                        await update.message.reply_text(f"📥 جاري تحميل الملف: {os.path.basename(corrected_file_path)}")

                        try:
                            # إرسال الملف وانتظار التأكيد
                            with open(corrected_file_path, "rb") as file_to_send:
                                message = await update.message.reply_document(
                                    document=file_to_send,
                                    filename=os.path.basename(corrected_file_path)
                                )
                            # تأكيد نجاح تحميل الملف
                            if message:
                                await update.message.reply_text(f"✅ تم تحميل الملف: {os.path.basename(corrected_file_path)}")
                        except Exception:
                            # إظهار الأخطاء فقط للملفات الأكبر من 20 ميجا
                            if file_size > 20 * 1024 * 1024:  # 20 ميجا
                                await update.message.reply_text(f"⚠️ حدث خطأ أثناء تحميل الملف {os.path.basename(corrected_file_path)}، الملف كبير ({file_size / (1024 * 1024):.2f} ميجابايت).")
                            continue
                    else:
                        # إذا كان الملف فارغًا، يتم تخطيه بدون أي رسالة
                        continue
                else:
                    # إذا كان الملف غير موجود، يتم تخطيه بدون أي رسالة
                    continue

                # تأخير بسيط بين الملفات لتجنب الضغط
                await asyncio.sleep(1)  # قلل التأخير لتسريع العملية
            
            # رسالة عند انتهاء جميع الملفات
            await update.message.reply_text("✅ ملفاتك قيد التحميل.")
            return ConversationHandler.END
        else:
            await update.message.reply_text("⚠️ لا توجد ملفات مرتبطة بهذا العميل أو جميع الملفات محذوفة.")
            return ConversationHandler.END

    except Exception as e:
        await update.message.reply_text(f"⚠️ حدث خطأ أثناء تحميل الملفات: {str(e)}")
        return ConversationHandler.END


# تعديل دالة cancel لتعيين العلم عند طلب الإنهاء
async def cancel(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """
    دالة لإنهاء المحادثة مع إظهار خيار لإعادة تشغيل البوت أو إنهائه بالكامل.
    """
    context.user_data["cancel_requested"] = True
    await update.message.reply_text("❌ تم إنهاء المحادثة.")
    return ConversationHandler.END


#--------------------------------------------------------------------------------------------------------------
#--------------------------------------------------------------------------------------------------------------
#--------------------------------------------------------------------------------------------------------------

#--------------------------------------------------------------------------------------------------------------

#-------------------------------------------------الاجتماعات  --------------------------------------------------------------

async def list_Meeting_contents(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    base_path = r"D:\File Terra\PublishAPI\Uploads\meeting"
    if os.path.exists(base_path):
        folders = os.listdir(base_path)
        contents = []

        for folder in folders:
            folder_path = os.path.join(base_path, folder)
            if os.path.isdir(folder_path):
                files = os.listdir(folder_path)
                contents.append(f"📁 {folder}:\n" + "\n".join([f"  - {file}" for file in files]))

        if contents:
            message = "\n\n".join(contents)
            await update.message.reply_text(f"📂 محتويات meeting:\n\n{message}")
        else:
            await update.message.reply_text("📂 لا توجد فولدرات أو ملفات في meeting.")
    else:
        await update.message.reply_text("📂 مسار Meeting غير موجود على السيرفر.")




async def download_meeting_start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    await update.message.reply_text("من فضلك أدخل كود العميل لتحميل اجتماعاته (مثل: ):")
    return DOWNLOAD_FILES

async def download_meeting(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    client_code = update.message.text.strip()

    if client_code == "إنهاء المحادثة":  # 🟥 (غير موجود في الكود الثاني)
        await cancel(update, context)
        return ConversationHandler.END

    if not client_code:
        await update.message.reply_text("كود العميل المدخل فارغ. من فضلك أدخل كود العميل.")
        return DOWNLOAD_FILES

    try:
        conn = connect_to_db()
        cursor = conn.cursor()
        query = """
        SELECT Sys_Files.FileUrl
        FROM Acc_Customers
        INNER JOIN Sys_Previews ON Acc_Customers.CustomerId = Sys_Previews.CustomerId
        INNER JOIN Sys_Files ON Sys_Previews.PreviewId = Sys_Files.PreviewId
        WHERE Acc_Customers.CustomerCode = ?
        AND Sys_Files.IsDeleted=0
        """
        cursor.execute(query, (client_code,))
        files = cursor.fetchall()
        conn.close()

        if files:
            for file in files:
                raw_file_path = file[0]
                corrected_file_path = raw_file_path.replace("/Uploads/Preview", "D:\\File Terra\\PublishAPI\\Uploads\\meeting").replace("/", "\\")

                if os.path.exists(corrected_file_path):
                    await update.message.reply_document(document=open(corrected_file_path, 'rb'))
                else:
                    await update.message.reply_text(f"⚠️ الملف غير موجود: {corrected_file_path}")
            return ConversationHandler.END
        else:
            await update.message.reply_text("⚠️ لا توجد ملفات لهذا العميل.")
            return ConversationHandler.END
    except Exception as e:
        await update.message.reply_text(f"⚠️ حدث خطأ أثناء تحميل الملفات: {str(e)}")
        return ConversationHandler.END


#***************************************************************************************************************************************************************
#***************************************************************************************************************************************************************
#***************************************************************************************************************************************************************
#***************************************************************************************************************************************************************


#----------------------------------------****بحث عميل***---------------------------------------------------

async def search_customer_start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    await update.message.reply_text("من فضلك أدخل كود العميل أو رقم الهاتف للبحث:")
    return SEARCH_CUSTOMER  
#--------------------------------------------------------------------------------------------------------------


async def search_customer(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    search_term = update.message.text.strip()  # قراءة نص البحث وإزالة المسافات الزائدة

    if search_term == "إنهاء المحادثة":           #-----------------------------انهاء
        await cancel(update, context)
        return ConversationHandler.END

    if not search_term:
        await update.message.reply_text("النص المدخل فارغ. من فضلك أدخل كود العميل أو رقم الهاتف.")
        await start(update, context)
        return ConversationHandler.END

    try:
        conn = connect_to_db() 
        cursor = conn.cursor() 

        query = """
        SELECT * 
        FROM Acc_Customers 
        INNER JOIN Sys_Branches ON Acc_Customers.BranchId = Sys_Branches.BranchId  
        INNER JOIN Acc_PayType ON Acc_Customers.PayTypeId = Acc_PayType.PayTypeId  
        INNER JOIN Sys_City ON Acc_Customers.CityId = Sys_City.CityId    
        INNER JOIN Sys_SocialMedia ON Acc_Customers.SocialMediaId = Sys_SocialMedia.SocialMediaId 
        INNER JOIN Sys_Users ON Acc_Customers.AddUser = Sys_Users.UserId 
        WHERE Acc_Customers.Status=1 
        AND Acc_Customers.IsDeleted=0 
        AND (CustomerCode = ? OR MainPhoneNo = ? OR SubMainPhoneNo = ?)""" 
        cursor.execute(query, (search_term, search_term, search_term))  #  
        customer = cursor.fetchone() 
        conn.close()  #

        if customer:  # 
            customer_info = f"""
            كود العميل : {customer[2]}
            الفرع: {customer[26]}
            الاسم (عربي): {customer[3]}
            الاسم (إنجليزي): {customer[4]}
            الهاتف 1: {customer[7]}
            الهاتف 2: {customer[8]}
            المنطقة : {customer[49]}
            العنوان: {customer[5]}
            رقم الهوية: {customer[9]}
            البريد الإلكتروني: {customer[6]}
            نوع الدفع : {customer[38]}
            عرفتنا عن طريق : {customer[60]}
            القائم بادخل العميل : {customer[70]}
            """
            await update.message.reply_text(customer_info)  
            await update.message.reply_text(" للبحث عن عميل اخر برجاء كتابة كود او رقم الهاتف")
            return SEARCH_CUSTOMER 
        else:
            await update.message.reply_text("لم يتم العثور على العميل باستخدام هذا الكود أو الرقم.")
        
        await start(update, context)  
        return ConversationHandler.END
    except Exception as e:
        await update.message.reply_text(f"حدث خطأ أثناء البحث: {str(e)}")
        await start(update, context)
        return ConversationHandler.END
#***************************************************************************************************************************************************************
#***************************************************************************************************************************************************************
#***************************************************************************************************************************************************************
#***************************************************************************************************************************************************************


#----------------------------------------****بحث موظف***---------------------------------------------------

async def search_Employees_start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    await update.message.reply_text("من فضلك أدخل كود الموظف أو رقم الهاتف للبحث:")
    return SEARCH_EMPLOYEES

# --------------------------------------------------------------------------------------------------------
async def search_Employees(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    search_term = update.message.text.strip()

    if search_term == "إنهاء المحادثة":
        await cancel(update, context)
        return ConversationHandler.END
    

    if not search_term:
        await update.message.reply_text("⚠️ النص المدخل فارغ. من فضلك أدخل كود الموظف أو رقم الهاتف.")
        await start(update, context)  # العودة إلى البداية
        return ConversationHandler.END

    try:
        conn = connect_to_db()
        if not conn:
            await update.message.reply_text("⚠️ تعذر الاتصال بقاعدة البيانات.")
            await start(update, context)  # العودة إلى البداية
            return ConversationHandler.END

        cursor = conn.cursor()
        query = """
        SELECT EmployeeId, Sys_Branches.NameAr, EmployeeCode, MainPhoneNo, SubMainPhoneNo, 
               NationalId, DirectManagerId, Salary, AppointmentDate, Address, 
               Emp_Employees.NameAr, Sys_Jobs.NameAr
        FROM Emp_Employees 
        INNER JOIN Sys_Branches ON Emp_Employees.BranchId = Sys_Branches.BranchId
        INNER JOIN Sys_Jobs ON Emp_Employees.JobId = Sys_Jobs.JobId
        WHERE Emp_Employees.Status = 1 
          AND Emp_Employees.IsDeleted = 0 
          AND (EmployeeCode = ? OR MainPhoneNo = ? OR SubMainPhoneNo = ?)
        """
        cursor.execute(query, (search_term, search_term, search_term))
        employee = cursor.fetchone()
        conn.close()

        if employee:
            employee_info = f"""
            اسم الفرع: {employee[1]}
            كود الموظف: {employee[2]}
            اسم الموظف: {employee[10]}
            رقم الهاتف 1: {employee[3]}
            رقم الهاتف 2: {employee[4]}
            الوظيفة: {employee[11]}
            المرتب: {employee[7]}
            تاريخ التعيين: {employee[8]}
            المدير المباشر: {employee[6]}
            العنوان: {employee[9]}
            """
            await update.message.reply_text(employee_info)
        else:
            await update.message.reply_text("⚠️ لم يتم العثور على الموظف.")

        await start(update, context)
        return ConversationHandler.END

    except Exception as e:
        await update.message.reply_text(f"⚠️ حدث خطأ: {str(e)}")
        await start(update, context)  
        return ConversationHandler.END
  #--------------------------------------------------------
  #---------------------start  موظفين يا معلم -----------------------------------

async def download_employee_files_start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    await update.message.reply_text("من فضلك أدخل كود الموظف لتحميل ملفاته:")
    return DOWNLOAD_FILES
    
    #------------------------تحميل ملف بكون موظف -----------------------------------
async def download_employee_files(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """
    تحميل الملفات المرتبطة بكود الموظف، مع عرض قائمة الملفات قبل التحميل.
    """
    employee_code = update.message.text.strip()

    if employee_code == "إنهاء المحادثة":
        await cancel(update, context)  # استدعاء دالة الإنهاء
        return ConversationHandler.END  # إنهاء المحادثة     


    if not employee_code:
        await update.message.reply_text("⚠️ كود الموظف المدخل فارغ. من فضلك أدخل كود الموظف.")
        return DOWNLOAD_FILES

    try:
        conn = connect_to_db()
        cursor = conn.cursor()

        query = """
        SELECT Sys_Files.FileUrl, Sys_Files.FileSize, Sys_Files.AddDate
        FROM Emp_Employees
        INNER JOIN Sys_Files ON Emp_Employees.EmployeeId = Sys_Files.EmployeeId
        WHERE Emp_Employees.EmployeeCode = ?
        ORDER BY Sys_Files.AddDate ASC
        """
        cursor.execute(query, (employee_code,))
        files = cursor.fetchall()
        conn.close()

        if files:
            # عرض قائمة بأسماء الملفات قبل البدء في التحميل
            file_names = [os.path.basename(file[0]) for file in files]
            file_list_message = "📋 الملفات التي سيتم تحميلها:\n" + "\n".join(file_names)
            await update.message.reply_text(file_list_message)

            for file in files:
                raw_file_path, file_size, add_date = file
                corrected_file_path = raw_file_path.replace("/Uploads", "D:\\File Terra\\PublishAPI\\Uploads").replace("/", "\\")

                if os.path.exists(corrected_file_path):
                    if file_size > 0:
                        # إرسال رسالة تفيد ببدء تحميل الملف
                        await update.message.reply_text(f"📥 جاري تحميل الملف: {os.path.basename(corrected_file_path)}")

                        try:
                            # إرسال الملف وانتظار التأكيد
                            with open(corrected_file_path, "rb") as file_to_send:
                                message = await update.message.reply_document(
                                    document=file_to_send,
                                    filename=os.path.basename(corrected_file_path)
                                )
                            # تأكيد نجاح تحميل الملف
                            if message:
                                await update.message.reply_text(f"✅ تم تحميل الملف: {os.path.basename(corrected_file_path)}")
                        except Exception:
                            # إظهار الأخطاء فقط للملفات الأكبر من 20 ميجا
                            if file_size > 20 * 1024 * 1024:  # 20 ميجا
                                await update.message.reply_text(f"⚠️ حدث خطأ أثناء تحميل الملف {os.path.basename(corrected_file_path)}، الملف كبير ({file_size / (1024 * 1024):.2f} ميجابايت).")
                            continue
                    else:
                        # إذا كان الملف فارغًا، يتم تخطيه بدون أي رسالة
                        continue
                else:
                    # إذا كان الملف غير موجود، يتم تخطيه بدون أي رسالة
                    continue

                # تأخير بسيط بين الملفات لتجنب الضغط
                await asyncio.sleep(2)
            
            # رسالة عند انتهاء جميع الملفات
            await update.message.reply_text("✅ تم الانتهاء من تحميل جميع الملفات.")
            return ConversationHandler.END
        else:
            await update.message.reply_text("⚠️ لا توجد ملفات مرتبطة بهذا الموظف.")
            return ConversationHandler.END

    except Exception:
        # تجاهل جميع الأخطاء العامة وعدم عرض أي رسائل
        return ConversationHandler.END


#----------------------------------------****استعلام عملاء**---------------------------------------------------

async def handle_general_inquiry(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """
    يعالج اختيار الفرع ويعرض قائمة العملاء مقسمة إلى رسائل أصغر.
    """
    branch_key = update.message.text.strip()
    branches_mapping = {
        "1": 3,  # Branch ID for التجمع
        "2": 2,  # Branch ID for مدينة نصر
    }

    if branch_key == "إنهاء المحادثة":
        await cancel(update, context)  # استدعاء دالة الإنهاء
        return ConversationHandler.END  # إنهاء المحادثة     


    if branch_key not in branches_mapping:
        await update.message.reply_text("⚠️ . من فضلك أدخل 1 للتجمع أو 2 لمدينة نصر.")
        return SELECT_BRANCH

    branch_id = branches_mapping[branch_key]
    branch_name = "التجمع" if branch_id == 3 else "مدينة نصر"

    try:
        conn = connect_to_db()
        cursor = conn.cursor()
        query = "SELECT CustomerCode, NameAr FROM Acc_Customers WHERE BranchId = ? AND IsDeleted = 0"
        cursor.execute(query, (branch_id,))
        customers = cursor.fetchall()
        conn.close()

        if customers:
            # تقسيم قائمة العملاء إلى دفعات صغيرة
            chunk_size = 100 # عدد العملاء في كل رسالة
            chunks = [customers[i:i + chunk_size] for i in range(0, len(customers), chunk_size)]

            for index, chunk in enumerate(chunks, start=1):
                customer_list = "\n".join([f"{customer[0]} - {customer[1]}" for customer in chunk])
                #customer_list = "\n".join([f"{i + 1}. {customer[0]} - {customer[1]}" for i, customer in enumerate(chunk)])
                await update.message.reply_text(f"📋 قائمة العملاء في فرع {branch_name} (صفحة {index}):\n{customer_list}")
                
                # تأخير بين كل رسالة
                await asyncio.sleep(1)
        else:
            await update.message.reply_text(f"⚠️ لا توجد بيانات للعملاء في فرع {branch_name}.")

        return ConversationHandler.END

    except Exception as e:
        await update.message.reply_text(f"⚠️ حدث خطأ أثناء الاستعلام: {str(e)}")
        return ConversationHandler.END


#########----------------------ااستعلام عملاء ----------الوظيفة 4 --------------------------#############################

async def show_social_media_customers(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    await start(update, context)  # إعادة إرسال الأزرار
    try:
        conn = connect_to_db()
        cursor = conn.cursor()
        
        query = """
        SELECT
            Sys_SocialMedia.SocialMediaId,
            Sys_SocialMedia.NameAr AS SocialMediaName,
            Sys_Branches.NameAr AS BranchName,
            COUNT(Acc_Customers.CustomerCode) AS NumberOfCustomers
        FROM
            Sys_SocialMedia
        INNER JOIN Acc_Customers
            ON Acc_Customers.SocialMediaId = Sys_SocialMedia.SocialMediaId
        INNER JOIN Sys_Branches
            ON Sys_Branches.BranchId = Acc_Customers.BranchId
        WHERE
            Acc_Customers.AddDate >= DATEADD(DAY, -100, GETDATE())
            AND Acc_Customers.Status = 1
            AND Acc_Customers.IsDeleted = 0
        GROUP BY
            Sys_SocialMedia.SocialMediaId,
            Sys_SocialMedia.NameAr,
            Sys_Branches.NameAr
        ORDER BY
            NumberOfCustomers DESC;
        """
        cursor.execute(query)
        results = cursor.fetchall()
        conn.close()

        if results:
            message = "📊 عدد العملاء لكل الوسائل اجتماعية:\n\n"
            for row in results:
                social_media_id = row[0]
                social_media_name = row[1]
                branch_name = row[2]
                customer_count = row[3]
                message += f"- {social_media_name} (فرع: {branch_name}): {customer_count}  \n"    #    message += f"- {social_media_name} (فرع: {branch_name}): {customer_count} عميل\n"
            await update.message.reply_text(message)
        else:
            await update.message.reply_text("لا توجد بيانات للعملاء في هذه الفترة.")
    except Exception as e:
        await update.message.reply_text(f"حدث خطأ أثناء تنفيذ الاستعلام: {str(e)}")
        

async def select_branch_for_social_media(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """
    يسأل المستخدم عن الفرع باستخدام الرقم لعرض بيانات وسائل التواصل.
    """
    branch_key = update.message.text.strip()

    # معالجة إنهاء المحادثة
    if branch_key == "إنهاء المحادثة":
        await cancel(update, context)
        return ConversationHandler.END

    await update.message.reply_text("من فضلك أدخل رقم الفرع:\n1- فرع التجمع\n2- فرع مدينة نصر")
    return SELECT_BRANCH



async def select_days_for_social_media(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """
    يستلم رقم الفرع من المستخدم ويطلب عدد الأيام لعرض بيانات وسائل التواصل.
    """
    branch_key = update.message.text.strip()

    # معالجة إنهاء المحادثة
    if branch_key == "إنهاء المحادثة":
        await cancel(update, context)
        return ConversationHandler.END
    """
    يستلم رقم الفرع من المستخدم ويطلب عدد الأيام لعرض بيانات وسائل التواصل.
    """

    
    branch_key = update.message.text.strip()
    branches_mapping = {
        "1": "التجمع",  # ربط الرقم 1 بفرع التجمع
        "2": "مدينة نصر",  # ربط الرقم 2 بفرع مدينة نصر
    }

    if branch_key not in branches_mapping:
        await update.message.reply_text("⚠️ اختيار غير صحيح. من فضلك أدخل 1 أو 2.")
        return SELECT_BRANCH
    

    context.user_data["selected_branch"] = branches_mapping[branch_key]
    await update.message.reply_text(f"تم اختيار فرع {branches_mapping[branch_key]}. من فضلك أدخل عدد الأيام (رقم فقط):")
    return SELECT_DAYS

 
async def show_customers_by_social_media(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """
    يعرض عدد العملاء حسب وسائل التواصل الاجتماعي في الفرع المختار وعدد الأيام.
    """
    try:
        days = int(update.message.text.strip())
        branch_name = context.user_data.get("selected_branch", "")

        if not branch_name:
            await update.message.reply_text("⚠️ لم يتم تحديد الفرع بشكل صحيح. يرجى المحاولة مرة أخرى.")
            return ConversationHandler.END

        conn = connect_to_db()
        cursor = conn.cursor()
        query = """
        SELECT 
    SM.NameAr AS SocialMediaName, 
    COUNT(C.CustomerCode) AS CustomerCount
FROM 
    Acc_Customers C
INNER JOIN 
    Sys_SocialMedia SM ON C.SocialMediaId = SM.SocialMediaId
INNER JOIN 
    Sys_Branches B ON C.BranchId = B.BranchId
WHERE 
    B.NameAr = ?  -- اسم الفرع كمدخل للدالة
    AND C.AddDate >= DATEADD(DAY, -?, GETDATE())  -- عدد الأيام كمدخل للدالة
    AND C.IsDeleted = 0  -- فلتر لاستبعاد العملاء المحذوفين
GROUP BY 
    SM.NameAr 
ORDER BY 
    CustomerCount DESC;

"""

        cursor.execute(query, (branch_name, days))
        results = cursor.fetchall()
        conn.close()

        if results:
            message = f"📊 عدد العملاء حسب وسائل التواصل الاجتماعي في فرع {branch_name} خلال {days} يوم:\n\n"
            for row in results:
                message += f"- {row[0]} : {row[1]} \n"
            await update.message.reply_text(message)
        else:
            await update.message.reply_text("⚠️ لا توجد بيانات للعملاء بناءً على هذه الفترة.")

        return ConversationHandler.END

    except Exception as e:
        await update.message.reply_text(f"⚠️ حدث خطأ أثناء تنفيذ الاستعلام: {str(e)}")
        return ConversationHandler.END

async def select_branch(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    branch_name = update.message.text.strip()
    valid_branches = ["التجمع", "مدينة نصر"]

    if branch_name not in valid_branches:
        await update.message.reply_text("⚠️ برجاء ادخال رقم الفرع (التجمع رقم 1 ) (مدينة نصر رقم 2)).")
        return SELECT_BRANCH

    context.user_data["selected_branch"] = branch_name
    print(f"Branch stored: {context.user_data['selected_branch']}")  # طباعة للتأكد

    await update.message.reply_text(f"✅ تم اختيار فرع {branch_name}. أدخل عدد الأيام (رقم فقط):")
    return SELECT_DAYS


#----------------عدد العملاء حسب المناطق--------------------------------
#----------------عدد العملاء حسب المناطق--------------------------------
#----------------عدد العملاء حسب المناطق--------------------------------


async def select_branch(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """
    يسأل المستخدم عن الفرع باستخدام الأرقام 1 أو 2.  
    """
     
    branch_key = update.message.text.strip()

    # معالجة إنهاء المحادثة
    if branch_key == "إنهاء المحادثة":  # 🟥 (غير موجود في الكود الثاني)
        await cancel(update, context)
        return ConversationHandler.END
     
    await update.message.reply_text("📍 من فضلك اختر الفرع:\n1 - فرع التجمع\n2 - فرع مدينة نصر")
    return SELECT_BRANCH


async def select_days(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """
    يستلم رقم الفرع من المستخدم ويطلب عدد الأيام.
    """
    branch_key = update.message.text.strip()
    branches_mapping = {
        "1": "التجمع",  # الرقم 1 لفرع التجمع
        "2": "مدينة نصر"  # الرقم 2 لفرع مدينة نصر
    }

    branch_key = update.message.text.strip()

    # معالجة إنهاء المحادثة
    if branch_key == "إنهاء المحادثة":  # 🟥 (غير موجود في الكود الثاني)
        await cancel(update, context)
        return ConversationHandler.END

    if branch_key not in branches_mapping:
        await update.message.reply_text("⚠️ اختيار غير صحيح. من فضلك أدخل 1 للتجمع أو 2 لمدينة نصر.")
        return SELECT_BRANCH

    # حفظ الفرع المختار في `context.user_data`
    context.user_data["selected_branch"] = branches_mapping[branch_key]
    await update.message.reply_text(f"✅ تم اختيار فرع {branches_mapping[branch_key]}. من فضلك أدخل عدد الأيام (رقم فقط):")
    return SELECT_DAYS


async def show_customers_by_city(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """
    يعرض عدد العملاء حسب المناطق في الفرع المختار وعدد الأيام.
    """
    try:
        days = int(update.message.text.strip())
        branch_name = context.user_data.get("selected_branch", "")

        if not branch_name:
            await update.message.reply_text("⚠️ لم يتم تحديد الفرع بشكل صحيح. يرجى المحاولة مرة أخرى.")
            return ConversationHandler.END

        conn = connect_to_db()
        cursor = conn.cursor()
        query = """
        SELECT
            Sys_City.NameAr AS CityName,
            COUNT(Acc_Customers.CustomerCode) AS CustomerCount
        FROM
            Acc_Customers
        INNER JOIN
            Sys_City ON Acc_Customers.CityId = Sys_City.CityId
        INNER JOIN
            Sys_Branches ON Acc_Customers.BranchId = Sys_Branches.BranchId
        WHERE
            Sys_Branches.NameAr = ? AND
            Acc_Customers.AddDate >= DATEADD(DAY, -?, GETDATE()) AND
            Acc_Customers.Status = 1 AND
            Acc_Customers.IsDeleted = 0
        GROUP BY
            Sys_City.NameAr
        ORDER BY
            CustomerCount DESC;
        """

        cursor.execute(query, (branch_name, days))
        results = cursor.fetchall()
        conn.close()

        if results:
            message = f"📊 عدد العملاء حسب المناطق في فرع {branch_name} خلال {days} يوم:\n\n"
            for row in results:
                city_name, customer_count = row
                message += f"- {city_name}: {customer_count}\n"
            await update.message.reply_text(message)
        else:
            await update.message.reply_text("⚠️ لا توجد بيانات للعملاء بناءً على هذه الفترة.")

        return ConversationHandler.END

    except Exception as e:
        await update.message.reply_text(f"⚠️ حدث خطأ أثناء تنفيذ الاستعلام: {str(e)}")
        return ConversationHandler.END
#-----------------------------------------------------------------------------------------
#-----------------------------------------------------------------------------------------
#-----------------------------------------------------------------------------------------
#-----------------------------------------------------------------------------------------










async def stop_bot(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    await update.message.reply_text("🛑 تم إيقاف البوت بالكامل. مع السلامة!")
    os._exit(0)

#-----------------------------------------------------

async def start_login(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    await update.message.reply_text("👤 من فضلك أدخل اسم المستخدم:")
    return LOGIN_USERNAME

async def get_username(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    username = update.message.text.strip()
    context.user_data['username'] = username
    await update.message.reply_text("🔑 الآن، أدخل كلمة المرور:")
    return LOGIN_PASSWORD

async def get_password(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    password = update.message.text.strip()
    username = context.user_data.get('username')

    try:
        conn = connect_to_db()
        cursor = conn.cursor()
        query = "SELECT FullName FROM Tuser WHERE Username = ? AND Password = ?"
        cursor.execute(query, (username, password))
        user = cursor.fetchone()
        conn.close()

        if user:
            await update.message.reply_text(f"✅ مرحباً {user[0]}! تم تسجيل دخولك بنجاح.")
        else:
            await update.message.reply_text("❌ اسم المستخدم أو كلمة المرور غير صحيحة. حاول مرة أخرى.")
        return ConversationHandler.END
    except Exception as e:
        await update.message.reply_text(f"⚠️ حدث خطأ أثناء تسجيل الدخول: {str(e)}")
        return ConversationHandler.END

async def start_login(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    await update.message.reply_text("👤 من فضلك أدخل اسم المستخدم:")
    return LOGIN_USERNAME

async def get_username(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    username = update.message.text.strip()
    context.user_data['username'] = username
    await update.message.reply_text("🔑 الآن، أدخل كلمة المرور:")
    return LOGIN_PASSWORD

async def get_password(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    password = update.message.text.strip()
    username = context.user_data.get('username')

    try:
        conn = connect_to_db()
        cursor = conn.cursor()
        query = "SELECT FullName FROM Tuser WHERE Username = ? AND Password = ?"
        cursor.execute(query, (username, password))
        user = cursor.fetchone()
        conn.close()

        if user:
            await update.message.reply_text(f"✅ مرحباً {user[0]}! تم تسجيل دخولك بنجاح.")
        else:
            await update.message.reply_text("❌ اسم المستخدم أو كلمة المرور غير صحيحة. حاول مرة أخرى.")
        return ConversationHandler.END
    except Exception as e:
        await update.message.reply_text(f"⚠️ حدث خطأ أثناء تسجيل الدخول: {str(e)}")
        return ConversationHandler.END

async def cancel(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """
    دالة لإنهاء المحادثة مع إظهار خيار لإعادة تشغيل البوت أو إنهائه بالكامل.
    """
    # إعداد خيارات المستخدم
    keyboard = [["نعم", "لا"]]
    reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=True)

    # رسالة إنهاء المحادثة
    await update.message.reply_text(
        "❌ تم إنهاء المحادثة. هل تريد البدء من جديد؟",
        reply_markup=reply_markup,
    )

    return ConversationHandler.END


# دالة لمعالجة رد المستخدم بعد إنهاء المحادثة
async def handle_restart_choice(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    user_choice = update.message.text.strip()

    if user_choice == "نعم":
        # إعادة تشغيل البوت مع إعادة زرار الواجهة الافتراضية
        await start(update, context)
    elif user_choice == "لا":
        # إزالة لوحة المفاتيح وعرض رسالة تنبيه
        await update.message.reply_text(
            "🚫 تم إيقاف البوت بالكامل. لن يتم قبول أي أوامر جديده ",
            reply_markup=ReplyKeyboardRemove(),
        )
        
        # إضافة علامة لإيقاف البوت
        context.bot_data["bot_stopped"] = True


# منع أي أوامر أخرى إذا كان البوت متوقفًا
async def block_commands_if_stopped(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    دالة لمنع تنفيذ الأوامر إذا كان البوت متوقفًا.
    """
    if context.bot_data.get("bot_stopped", False):
        await update.message.reply_text("🚫 البوت متوقف. يرجى استخدام /start لإعادة تشغيل البوت.")
    else:
        # إذا لم يكن البوت متوقفًا، يتم السماح للأوامر بالعمل
        await update.message.reply_text("⚠️ الأمر الذي أدخلته غير معروف.")


# تعديل دالة start لإعادة تفعيل البوت
async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """
    دالة لإعادة تشغيل البوت.
    """
    # إعادة تعيين حالة البوت
    context.bot_data["bot_stopped"] = False

    # زر الواجهة الافتراضية
    keyboard = [
        [KeyboardButton("بحث عميل"), KeyboardButton("استعلام عن العملاء")],
        [KeyboardButton("عدد عملاء وسائل التواصل"), KeyboardButton("عدد العملاء حسب المناطق")],
        [KeyboardButton("بحث موظف"), KeyboardButton("تحميل ملفات موظف")],
        [KeyboardButton("تحميل معاينات"), KeyboardButton("تحميل اجتماعات")],    
        [KeyboardButton("إنهاء المحادثة")],    
    ]
    reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=False)

    await update.message.reply_text("✅ تم إعادة تشغيل البوت. اختر أحد الخيارات التالية:", reply_markup=reply_markup)


async def show_commands(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    commands = """
    🛠️ الأوامر المتاحة في البوت:
    - /start: بدء البوت
    - /run: تشغيل البوت
    - /off: إيقاف البوت
    - /prof: تنفيذ الأمر الخاص
    - بحث عميل: للبحث عن العملاء
    - بحث موظف: للبحث عن الموظفين
    - تحميل معاينات: لتحميل معاينات العملاء
    - تحميل اجتماعات: لتحميل اجتماعات العملاء
    - استعلام عن العملاء: لعرض بيانات العملاء
    - عدد عملاء وسائل التواصل: لعرض العملاء بناءً على وسائل التواصل
    """
    await update.message.reply_text(commands)




#-------------------------------    ali0
#-------------------------------    ali0
def main():
    # فك تشفير التوكن
    decrypted_token = decrypt_token()
    if not decrypted_token:
        print("فشل في فك تشفير التوكن. تأكد من وجود الملفات.")
        return

    # إنشاء التطبيق
    application = Application.builder().token(decrypted_token).build()

    # إنشاء أو الحصول على حلقة الأحداث بطريقة متوافقة
    try:
        loop = asyncio.get_running_loop()  # استخدام `get_running_loop` بدلًا من `get_event_loop`
    except RuntimeError:  # إذا لم توجد حلقة أحداث حالية
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

    # تعريف Handlers والإضافات
    application.add_handler(ConversationHandler(
        entry_points=[MessageHandler(filters.Regex("^تحميل معاينات$"), download_previews_start)],
        states={DOWNLOAD_FILES: [MessageHandler(filters.TEXT & ~filters.COMMAND, download_previews)]},
        fallbacks=[MessageHandler(filters.Regex("^(إلغاء|إنهاء المحادثة)$"), cancel)],
    ))
    application.add_handler(ConversationHandler(
    entry_points=[MessageHandler(filters.Regex("^تحميل ملفات موظف$"), download_employee_files_start)],
    states={DOWNLOAD_FILES: [MessageHandler(filters.TEXT & ~filters.COMMAND, download_employee_files)]},
    fallbacks=[MessageHandler(filters.Regex("^(إلغاء|إنهاء المحادثة)$"), cancel)],
))
    application.add_handler(ConversationHandler(
    entry_points=[MessageHandler(filters.Regex("^استعلام عن العملاء$"), handle_general_inquiry)],
    states={
        SELECT_BRANCH: [MessageHandler(filters.TEXT & ~filters.COMMAND, handle_general_inquiry)],
    },
    fallbacks=[MessageHandler(filters.Regex("^(إلغاء|إنهاء المحادثة)$"), cancel)],
))


    application.add_handler(ConversationHandler(
        entry_points=[MessageHandler(filters.Regex("^تحميل اجتماعات$"), download_meeting_start)],
        states={DOWNLOAD_FILES: [MessageHandler(filters.TEXT & ~filters.COMMAND, download_meeting)]},
        fallbacks=[MessageHandler(filters.Regex("^(إلغاء|إنهاء المحادثة)$"), cancel)],
    ))

    application.add_handler(MessageHandler(filters.Regex("^استعلام عن محتويات Preview$"), list_preview_contents))

    application.add_handler(MessageHandler(filters.Regex("^استعلام عن محتويات Meeting$"), list_Meeting_contents))

    application.add_handler(ConversationHandler(
        entry_points=[MessageHandler(filters.Regex("^بحث عميل$"), search_customer_start)],
        states={SEARCH_CUSTOMER: [MessageHandler(filters.TEXT & ~filters.COMMAND, search_customer)]},
        fallbacks=[MessageHandler(filters.Regex("^(إلغاء|إنهاء المحادثة)$"), cancel)],
    ))

    search_employees_handler = ConversationHandler(
        entry_points=[MessageHandler(filters.Regex("^بحث موظف$"), search_Employees_start)],
        states={SEARCH_EMPLOYEES: [MessageHandler(filters.TEXT & ~filters.COMMAND, search_Employees)]},
        fallbacks=[MessageHandler(filters.Regex("^(إلغاء|إنهاء المحادثة)$"), cancel)],
    )
     
    
    application.add_handler(search_employees_handler)

    application.add_handler(ConversationHandler(
    entry_points=[MessageHandler(filters.Regex("^عدد العملاء حسب المناطق$"), select_branch)],
    states={
        SELECT_BRANCH: [MessageHandler(filters.TEXT & ~filters.COMMAND, select_days)],
        SELECT_DAYS: [MessageHandler(filters.TEXT & ~filters.COMMAND, show_customers_by_city)],
    },
    fallbacks=[MessageHandler(filters.Regex("^(إلغاء|إنهاء المحادثة)$"), cancel)],
))

    application.add_handler(ConversationHandler(
    entry_points=[CommandHandler("run", start)],
    states={},
    fallbacks=[CommandHandler("run", start)],
))


    application.add_handler(ConversationHandler(
    entry_points=[CommandHandler("off", cancel)],
    states={},
    fallbacks=[CommandHandler("off", cancel)],
))
      

    application.add_handler(ConversationHandler(
    entry_points=[CommandHandler("terra", start)],
    states={},
    fallbacks=[CommandHandler("terra", start)],
))


    application.add_handler(ConversationHandler(
    entry_points=[MessageHandler(filters.Regex("^عدد عملاء وسائل التواصل$"), select_branch_for_social_media)],
    states={
        SELECT_BRANCH: [MessageHandler(filters.TEXT & ~filters.COMMAND, select_days_for_social_media)],
        SELECT_DAYS: [MessageHandler(filters.TEXT & ~filters.COMMAND, show_customers_by_social_media)],
    },
    fallbacks=[MessageHandler(filters.Regex("^(إلغاء|إنهاء المحادثة)$"), cancel)],
))
    
    
     
    application.add_handler(MessageHandler(filters.Regex("^(نعم|لا)$"), handle_restart_choice))
    application.add_handler(CommandHandler("start", start))  # للأمر /start
    application.add_handler(CommandHandler("prof", custom_command))  # للأمر /prof
    application.add_handler(CommandHandler("run", start))  # للأمر /run
    application.add_handler(CommandHandler("terra", start))  # للأمر /terra
    application.add_handler(MessageHandler(filters.Regex("^عرض الأوامر$"), show_commands))
    application.add_handler(CommandHandler("r", start))  # للأمر /terra
    application.add_handler(CommandHandler("e", cancel))  # للأمر /terra
    application.add_handler(CommandHandler("pr", list_preview_contents))  # للأمر /prof
    application.add_handler(CommandHandler("me", list_Meeting_contents))  # للأمر /prof
    application.add_handler(CommandHandler("st", stop_bot))  # للأمر /prof
    application.add_handler(MessageHandler(filters.Regex("^إيقاف البوت$"), stop_bot))

    application.add_handler(MessageHandler(filters.Regex("^إنهاء المحادثة$"), cancel))
    # تشغيل التطبيق
    application.run_polling()

if __name__ == "__main__":
    main()
