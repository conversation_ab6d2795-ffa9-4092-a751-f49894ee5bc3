from cryptography.fernet import Fernet

# إنشاء مفتاح التشفير
key = Fernet.generate_key()
with open('encryption_key.key', 'wb') as key_file:
    key_file.write(key)

# تشفير التوكن
token = "7556046481:AAEMttgLYzrMu1q2ogRlgsV8mgscqZKbbcY"
fernet = Fernet(key)
encrypted_token = fernet.encrypt(token.encode())

with open('encrypted_token.txt', 'wb') as token_file:
    token_file.write(encrypted_token)

print("تم إنشاء ملفات التشفير بنجاح!")
