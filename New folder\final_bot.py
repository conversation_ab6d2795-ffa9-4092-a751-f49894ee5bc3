#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import logging
from telegram import Update
from telegram.ext import Application, CommandHandler, ContextTypes

# إعداد الـ logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# التوكن
TOKEN = "7556046481:AAEMttgLYzrMu1q2ogRlgsV8mgscqZKbbcY"

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """دالة البداية"""
    user_id = update.effective_user.id
    user_name = update.effective_user.first_name
    
    print(f"[INFO] تم استقبال /start من المستخدم: {user_name} (ID: {user_id})")
    logger.info(f"تم استقبال /start من المستخدم: {user_name} (ID: {user_id})")
    
    welcome_msg = f"""
🎉 مرحباً {user_name}!

✅ بوت TeERA يعمل بنجاح!
📅 التاريخ: {update.message.date}
🆔 معرف المستخدم: {user_id}

استخدم /test لاختبار البوت
    """
    
    await update.message.reply_text(welcome_msg)

async def test(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """دالة الاختبار"""
    user_id = update.effective_user.id
    user_name = update.effective_user.first_name
    
    print(f"[INFO] تم استقبال /test من المستخدم: {user_name} (ID: {user_id})")
    logger.info(f"تم استقبال /test من المستخدم: {user_name} (ID: {user_id})")
    
    await update.message.reply_text("✅ البوت يعمل بشكل ممتاز! 🚀")

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🚀 بدء تشغيل بوت TeERA")
    print("=" * 50)
    
    try:
        print("🔄 إنشاء تطبيق التليجرام...")
        application = Application.builder().token(TOKEN).build()
        
        print("🔄 إضافة معالجات الأوامر...")
        application.add_handler(CommandHandler("start", start))
        application.add_handler(CommandHandler("test", test))
        
        print("✅ تم إعداد البوت بنجاح")
        print("📱 البوت جاهز لاستقبال الرسائل")
        print("💡 أرسل /start في التليجرام لبدء الاستخدام")
        print("⏹️ اضغط Ctrl+C لإيقاف البوت")
        print("=" * 50)
        
        # تشغيل البوت
        application.run_polling(drop_pending_updates=True)
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
        logger.error(f"خطأ في تشغيل البوت: {e}")

if __name__ == "__main__":
    main()
