#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import subprocess

print("Searching for Python installations...")
print("=" * 50)

# Current Python
print(f"Current Python: {sys.executable}")
print(f"Python Version: {sys.version}")
print(f"Python Path: {sys.path[0] if sys.path else 'Unknown'}")

print("\nTesting package installations...")

# Test packages
packages = ['telegram', 'pyodbc', 'cryptography']
for package in packages:
    try:
        __import__(package)
        print(f"[OK] {package} - Available")
    except ImportError:
        print(f"[MISSING] {package} - Missing")

print("\nSearching for other Python installations...")

# Common Python paths
common_paths = [
    r"C:\Python39\python.exe",
    r"C:\Python310\python.exe",
    r"C:\Python311\python.exe",
    r"C:\Python312\python.exe",
    r"C:\Users\<USER>\AppData\Local\Programs\Python\Python39\python.exe",
    r"C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe",
    r"C:\Users\<USER>\AppData\Local\Programs\Python\Python311\python.exe",
    r"C:\Users\<USER>\AppData\Local\Programs\Python\Python312\python.exe",
]

for path in common_paths:
    expanded_path = os.path.expandvars(path)
    if os.path.exists(expanded_path):
        print(f"Found: {expanded_path}")

print("\nInstructions for VS Code:")
print("1. Press Ctrl+Shift+P")
print("2. Type: Python: Select Interpreter")
print("3. Choose the Python path that has the packages installed")
print(f"4. Current working Python: {sys.executable}")

input("\nPress Enter to exit...")
