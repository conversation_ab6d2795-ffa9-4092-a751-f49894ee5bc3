@echo off
title Fix Python and Install Packages
cls

echo Fixing Python installation and installing packages...
echo.

REM Try different ways to install packages
echo Method 1: Using python -m pip
"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" -m pip install python-telegram-bot pyodbc cryptography

if %errorlevel% neq 0 (
    echo.
    echo Method 2: Using py launcher
    py -m pip install python-telegram-bot pyodbc cryptography
)

if %errorlevel% neq 0 (
    echo.
    echo Method 3: Direct python command
    python -m pip install python-telegram-bot pyodbc cryptography
)

echo.
echo Testing installation...
"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" -c "import telegram; print('telegram: OK')"
"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" -c "import pyodbc; print('pyodbc: OK')"
"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" -c "import cryptography; print('cryptography: OK')"

echo.
echo Done! Now configure VS Code:
echo.
echo 1. In VS Code press Ctrl+Shift+P
echo 2. Type: Python: Select Interpreter
echo 3. Choose: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe
echo 4. Press Ctrl+Shift+P again
echo 5. Type: Developer: Reload Window
echo.

pause
