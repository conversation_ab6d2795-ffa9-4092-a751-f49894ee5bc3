@echo off
title Fix VS Code Python Environment
cls

echo ================================================
echo        Fix VS Code Python Environment
echo ================================================
echo.

echo Current Python information:
python --version
echo.
echo Python location:
where python
echo.

echo Installing packages for current Python...
echo.

pip install --upgrade pip
pip install python-telegram-bot
pip install pyodbc
pip install cryptography

echo.
echo Installation complete!
echo.

echo Testing installations...
python find_python.py

echo.
echo Next steps for VS Code:
echo 1. Press Ctrl+Shift+P in VS Code
echo 2. Type: Python: Select Interpreter
echo 3. Choose the Python path shown above
echo 4. Reload VS Code window (Ctrl+Shift+P - Developer: Reload Window)
echo.

pause
