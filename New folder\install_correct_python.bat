@echo off
title Install Packages - Correct Python
cls

echo Installing packages using correct Python version...
echo.

set PYTHON_EXE="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"

echo Using Python: %PYTHON_EXE%
%PYTHON_EXE% --version
echo.

echo Installing python-telegram-bot...
%PYTHON_EXE% -m pip install python-telegram-bot

echo.
echo Installing pyodbc...
%PYTHON_EXE% -m pip install pyodbc

echo.
echo Installing cryptography...
%PYTHON_EXE% -m pip install cryptography

echo.
echo Testing installations...
%PYTHON_EXE% -c "import telegram; print('telegram: SUCCESS')"
%PYTHON_EXE% -c "import pyodbc; print('pyodbc: SUCCESS')"
%PYTHON_EXE% -c "import cryptography; print('cryptography: SUCCESS')"

echo.
echo All packages installed successfully!
echo.

pause
