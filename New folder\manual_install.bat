@echo off
title Manual Package Installation
cls

echo Manual installation of Python packages...
echo.

REM Download and install packages manually
echo Downloading pip installer...
curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py

echo.
echo Installing pip...
"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" get-pip.py

echo.
echo Installing packages...
"C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe" -m pip install python-telegram-bot pyodbc cryptography

echo.
echo Cleaning up...
del get-pip.py

echo.
echo Installation complete!
echo.

pause
