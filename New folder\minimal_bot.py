#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("Starting minimal bot...")

try:
    from telegram import Update
    from telegram.ext import Application, CommandHandler, ContextTypes
    print("Telegram imports successful")
except ImportError as e:
    print(f"Import error: {e}")
    input("Press Enter to exit...")
    exit(1)

# Token
TOKEN = "7556046481:AAEMttgLYzrMu1q2ogRlgsV8mgscqZKbbcY"

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Start command"""
    user_name = update.effective_user.first_name
    print(f"Received /start from: {user_name}")
    
    await update.message.reply_text(
        f"Hello {user_name}!\n\n"
        "TeERA Bot is working!\n"
        "Use /test to test the bot"
    )

async def test(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """Test command"""
    user_name = update.effective_user.first_name
    print(f"Received /test from: {user_name}")
    
    await update.message.reply_text("Bo<PERSON> is working perfectly!")

def main():
    """Main function"""
    print("Creating application...")
    
    try:
        app = Application.builder().token(TOKEN).build()
        
        app.add_handler(CommandHandler("start", start))
        app.add_handler(CommandHandler("test", test))
        
        print("Bot setup complete")
        print("Bot is running...")
        print("Send /start in Telegram")
        print("Press Ctrl+C to stop")
        print("=" * 40)
        
        app.run_polling(drop_pending_updates=True)
        
    except Exception as e:
        print(f"Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
