#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TeERA Bot - New Clean Version
بوت تليجرام جديد ومنظم للتعامل مع قاعدة بيانات Terra
"""

import os
import sys
import logging
from datetime import datetime

# إعداد الـ logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('terra_bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def check_requirements():
    """فحص المتطلبات قبل بدء البوت"""
    print("🔍 فحص المتطلبات...")
    
    required_packages = ['telegram', 'pyodbc', 'cryptography']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} - متوفر")
        except ImportError:
            print(f"❌ {package} - مفقود")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ المكتبات المفقودة: {', '.join(missing_packages)}")
        print("💡 شغل install_packages.bat لتثبيت المكتبات")
        return False
    
    print("✅ جميع المتطلبات متوفرة")
    return True

def load_config():
    """تحميل إعدادات البوت"""
    print("📋 تحميل إعدادات البوت...")
    
    config = {}
    
    # تحميل التوكن
    try:
        with open('CREDINTEL.txt', 'r', encoding='utf-8') as file:
            for line in file:
                if line.startswith('token:'):
                    config['token'] = line.split(':', 1)[1].strip()
                    print("✅ تم تحميل التوكن")
                    break
    except FileNotFoundError:
        print("❌ ملف CREDINTEL.txt غير موجود")
        return None
    
    # تحميل بيانات قاعدة البيانات
    try:
        with open('db_credentials.txt', 'r', encoding='utf-8') as file:
            for line in file:
                if '=' in line:
                    key, value = line.strip().split('=', 1)
                    config[key.lower()] = value
        print("✅ تم تحميل بيانات قاعدة البيانات")
    except FileNotFoundError:
        print("❌ ملف db_credentials.txt غير موجود")
        return None
    
    return config

def test_database_connection(config):
    """اختبار الاتصال بقاعدة البيانات"""
    print("🔗 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        import pyodbc
        
        # إعدادات الاتصال الجديدة
        server = "alisamaraa.ddns.net,4100"
        database = "Terra"
        user = "sa"
        password = "@a123admin4"

        conn_str = f'DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={user};PWD={password}'
        
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        # اختبار بسيط
        cursor.execute("SELECT COUNT(*) FROM Acc_Customers WHERE IsDeleted=0")
        result = cursor.fetchone()

        if result and result[0] is not None:
            customer_count = result[0]
            print(f"✅ الاتصال ناجح - عدد العملاء: {customer_count:,}")
        else:
            print("✅ الاتصال ناجح - لا توجد بيانات عملاء")

        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ فشل الاتصال: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🚀 بدء تشغيل TeERA Bot الجديد")
    print("=" * 50)
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # فحص المتطلبات
    if not check_requirements():
        input("\nاضغط Enter للخروج...")
        return
    
    print()
    
    # تحميل الإعدادات
    config = load_config()
    if not config:
        print("❌ فشل في تحميل الإعدادات")
        input("\nاضغط Enter للخروج...")
        return
    
    print()
    
    # اختبار قاعدة البيانات
    if not test_database_connection(config):
        print("❌ فشل في الاتصال بقاعدة البيانات")
        input("\nاضغط Enter للخروج...")
        return
    
    print()
    print("✅ جميع الفحوصات نجحت!")
    print("🔄 بدء تشغيل البوت...")
    
    # استيراد وتشغيل البوت
    try:
        from telegram.ext import Application, CommandHandler
        from telegram import Update
        from telegram.ext import ContextTypes

        # دالة /start مع قائمة الأزرار
        async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
            if update.effective_user and update.message:
                user_name = update.effective_user.first_name or "مستخدم"
                print(f"📨 استقبال /start من: {user_name}")

                from telegram import ReplyKeyboardMarkup, KeyboardButton

                # إنشاء الأزرار
                keyboard = [
                    [KeyboardButton("🔍 بحث عميل"), KeyboardButton("📊 استعلام عن العملاء")],
                    [KeyboardButton("📱 عدد عملاء وسائل التواصل"), KeyboardButton("� عدد العملاء حسب المناطق")],
                    [KeyboardButton("👨‍💼 بحث موظف"), KeyboardButton("📁 تحميل ملفات موظف")],
                    [KeyboardButton("📋 تحميل معاينات"), KeyboardButton("🤝 تحميل اجتماعات")],
                    [KeyboardButton("❌ إنهاء المحادثة")],
                ]

                reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=False, resize_keyboard=True)

                welcome_msg = f"""🌟 مرحباً بك في بوت TeERA الجديد! 🌟

👤 المستخدم: {user_name}
📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}
🚀 الإصدار: نظيف ومحسن

اختر أحد الخيارات التالية:"""

                await update.message.reply_text(welcome_msg, reply_markup=reply_markup)

        # دالة اختبار قاعدة البيانات
        async def test_db(update: Update, context: ContextTypes.DEFAULT_TYPE):
            if update.effective_user and update.message:
                user_name = update.effective_user.first_name or "مستخدم"
                print(f"📨 استقبال /test من: {user_name}")

                await update.message.reply_text("🔄 جاري اختبار قاعدة البيانات...")

                if test_database_connection(config):
                    await update.message.reply_text("✅ قاعدة البيانات تعمل بنجاح!")

                    # فحص الفروع والعملاء
                    try:
                        import pyodbc

                        # إعدادات الاتصال الجديدة
                        server = "alisamaraa.ddns.net,4100"
                        database = "Terra"
                        user_db = "sa"
                        password_db = "@a123admin4"

                        conn_str = f'DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={user_db};PWD={password_db}'

                        conn = pyodbc.connect(conn_str)
                        cursor = conn.cursor()

                        # فحص الفروع
                        cursor.execute("SELECT BranchId, NameAr FROM Sys_Branches")
                        branches = cursor.fetchall()

                        # فحص العملاء
                        cursor.execute("SELECT COUNT(*) FROM Acc_Customers WHERE IsDeleted=0")
                        result1 = cursor.fetchone()
                        total_customers = result1[0] if result1 else 0

                        cursor.execute("SELECT COUNT(*) FROM Acc_Customers WHERE IsDeleted=0 AND Status=1")
                        result2 = cursor.fetchone()
                        active_customers = result2[0] if result2 else 0

                        # فحص عميل 818
                        cursor.execute("SELECT CustomerCode, NameAr, Status, IsDeleted, BranchId FROM Acc_Customers WHERE CustomerCode = '818'")
                        customer_818 = cursor.fetchone()

                        conn.close()

                        # تكوين الرسالة
                        info_msg = f"""📊 **معلومات قاعدة البيانات:**

🏢 **الفروع المتاحة:**
"""
                        for branch in branches:
                            info_msg += f"• {branch[1]} (ID: {branch[0]})\n"

                        info_msg += f"""
👥 **العملاء:**
• إجمالي العملاء: {total_customers:,}
• العملاء النشطين: {active_customers:,}

🔍 **فحص العميل 818:**
"""
                        if customer_818:
                            info_msg += f"• الكود: {customer_818[0]}\n"
                            info_msg += f"• الاسم: {customer_818[1] or 'غير محدد'}\n"
                            info_msg += f"• الحالة: {'نشط' if customer_818[2] == 1 else 'غير نشط'}\n"
                            info_msg += f"• محذوف: {'نعم' if customer_818[3] == 1 else 'لا'}\n"
                            info_msg += f"• الفرع: {customer_818[4]}\n"
                        else:
                            info_msg += "• العميل 818 غير موجود في قاعدة البيانات"

                        await update.message.reply_text(info_msg, parse_mode='Markdown')

                    except Exception as e:
                        await update.message.reply_text(f"❌ خطأ في فحص البيانات: {str(e)}")

                else:
                    await update.message.reply_text("❌ مشكلة في الاتصال بقاعدة البيانات")

        # دالة بحث العملاء
        async def search_customer(update: Update, context: ContextTypes.DEFAULT_TYPE):
            if update.effective_user and update.message:
                user_name = update.effective_user.first_name or "مستخدم"
                print(f"📨 طلب بحث عميل من: {user_name}")

                await update.message.reply_text(
                    "🔍 **بحث عن عميل**\n\n"
                    "من فضلك أدخل:\n"
                    "• كود العميل\n"
                    "• رقم الهاتف الأساسي\n"
                    "• رقم الهاتف الفرعي\n\n"
                    "مثال: 12345 أو 01012345678\n\n"
                    "💡 للإلغاء اكتب: إلغاء أو /start",
                    parse_mode='Markdown'
                )

                # حفظ حالة المستخدم
                if context.user_data is not None:
                    context.user_data['waiting_for'] = 'customer_search'

        # دالة معالجة النصوص (للبحث)
        async def handle_text(update: Update, context: ContextTypes.DEFAULT_TYPE):
            if update.effective_user and update.message and update.message.text:
                user_input = update.message.text.strip()
                user_name = update.effective_user.first_name or "مستخدم"

                # التحقق من حالة المستخدم
                waiting_for = context.user_data.get('waiting_for') if context.user_data else None

                # معالجة اختيار الفروع
                if waiting_for in ['branch_selection_customers', 'branch_selection_areas', 'branch_selection_social']:
                    if user_input in ['1', '2', '3']:
                        branch_names = {
                            '1': 'فرع التجمع',
                            '2': 'فرع مدينة نصر',
                            '3': 'للكل'
                        }

                        selected_branch = branch_names[user_input]
                        await update.message.reply_text(f"✅ تم اختيار {user_input} {selected_branch}")

                        # حفظ اختيار الفرع
                        if context.user_data is not None:
                            context.user_data['selected_branch'] = user_input
                            context.user_data['branch_name'] = selected_branch

                        # طلب عدد الأيام
                        await update.message.reply_text(
                            f"📅 من فضلك ادخل عدد ايام مده البحث المختاره او للكل اضغط # لفرع: {selected_branch}"
                        )

                        # تحديث الحالة لانتظار عدد الأيام
                        if context.user_data is not None:
                            if waiting_for == 'branch_selection_customers':
                                context.user_data['waiting_for'] = 'days_customers'
                            elif waiting_for == 'branch_selection_areas':
                                context.user_data['waiting_for'] = 'days_areas'
                            elif waiting_for == 'branch_selection_social':
                                context.user_data['waiting_for'] = 'days_social'

                        return
                    else:
                        await update.message.reply_text("⚠️ من فضلك اختر رقم صحيح: 1 أو 2 أو 3")
                        return

                # معالجة عدد الأيام
                elif waiting_for in ['days_customers', 'days_areas', 'days_social']:
                    if user_input == '#':
                        days_text = "للكل"
                        days_value = None
                    else:
                        try:
                            days_value = int(user_input)
                            days_text = f"{days_value} ايام"
                        except ValueError:
                            await update.message.reply_text("⚠️ من فضلك أدخل رقم صحيح أو # للكل")
                            return

                    branch_name = context.user_data.get('branch_name', 'غير محدد') if context.user_data else 'غير محدد'
                    selected_branch = context.user_data.get('selected_branch', '1') if context.user_data else '1'

                    # تنفيذ الاستعلام حسب النوع
                    await update.message.reply_text("🔄 جاري تحضير التقرير...")

                    try:
                        import pyodbc

                        # إعدادات الاتصال الجديدة
                        server = "alisamaraa.ddns.net,4100"
                        database = "Terra"
                        user_db = "sa"
                        password_db = "@a123admin4"

                        conn_str = f'DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={user_db};PWD={password_db}'

                        conn = pyodbc.connect(conn_str)
                        cursor = conn.cursor()

                        # تحديد شرط الفرع
                        if selected_branch == '3':  # للكل
                            branch_condition = ""
                            branch_params = []
                        else:
                            branch_id = '3' if selected_branch == '1' else '2'  # التجمع=3, مدينة نصر=2
                            branch_condition = "AND c.BranchId = ?"
                            branch_params = [branch_id]

                        # تحديد شرط التاريخ
                        if days_value is None:  # للكل
                            date_condition = ""
                            date_params = []
                        else:
                            date_condition = "AND c.AddDate >= DATEADD(day, -?, GETDATE())"
                            date_params = [days_value]

                        if waiting_for == 'days_customers':
                            # استعلام العملاء - كود العميل + اسم العميل (عدد محدود)
                            query = f"""
                            SELECT TOP 30
                                c.CustomerCode,
                                c.NameAr,
                                c.MainPhoneNo,
                                b.NameAr as BranchName,
                                c.AddDate
                            FROM Acc_Customers c
                            LEFT JOIN Sys_Branches b ON c.BranchId = b.BranchId
                            WHERE c.Status=1 AND c.IsDeleted=0 {branch_condition} {date_condition}
                            ORDER BY c.AddDate DESC
                            """

                            cursor.execute(query, branch_params + date_params)
                            results = cursor.fetchall()

                            if results:
                                report = f"📊 **استعلام عن العملاء في {branch_name} خلال {days_text}:**\n\n"

                                for i, row in enumerate(results, 1):
                                    customer_code = row[0] or 'غير محدد'
                                    customer_name = row[1] or 'غير محدد'
                                    phone = row[2] or 'غير محدد'
                                    branch_name_result = row[3] or 'غير محدد'
                                    add_date = row[4].strftime('%Y-%m-%d') if row[4] else 'غير محدد'

                                    report += f"{i}. 🆔 `{customer_code}` - {customer_name}\n"
                                    report += f"📞 `{phone}` | 🏢 {branch_name_result}\n\n"

                                report += f"📈 **إجمالي:** {len(results)} عميل"

                                # إرسال التقرير مباشرة (30 عميل فقط)
                                await update.message.reply_text(report, parse_mode='Markdown')
                                    # إرسال النصف الأول
                                    mid_point = len(results) // 2
                                    first_half = f"📊 **استعلام عن العملاء في {branch_name} خلال {days_text}:** (الجزء الأول)\n\n"

                                    for i, row in enumerate(results[:mid_point], 1):
                                        customer_code = row[0] or 'غير محدد'
                                        customer_name = row[1] or 'غير محدد'
                                        phone = row[2] or 'غير محدد'
                                        branch_name_result = row[3] or 'غير محدد'
                                        add_date = row[4].strftime('%Y-%m-%d') if row[4] else 'غير محدد'

                                        first_half += f"**{i}.** 🆔 `{customer_code}` - 👤 **{customer_name}**\n"
                                        first_half += f"📞 `{phone}` | 🏢 {branch_name_result} | � {add_date}\n"
                                        first_half += "─────────────────\n"

                                    await update.message.reply_text(first_half, parse_mode='Markdown')

                                    # إرسال النصف الثاني
                                    second_half = f"📊 **استعلام عن العملاء في {branch_name} خلال {days_text}:** (الجزء الثاني)\n\n"

                                    for i, row in enumerate(results[mid_point:], mid_point + 1):
                                        customer_code = row[0] or 'غير محدد'
                                        customer_name = row[1] or 'غير محدد'
                                        phone = row[2] or 'غير محدد'
                                        branch_name_result = row[3] or 'غير محدد'
                                        add_date = row[4].strftime('%Y-%m-%d') if row[4] else 'غير محدد'

                                        second_half += f"**{i}.** 🆔 `{customer_code}` - 👤 **{customer_name}**\n"
                                        second_half += f"📞 `{phone}` | 🏢 {branch_name_result} | � {add_date}\n"
                                        second_half += "─────────────────\n"

                                    second_half += f"\n📈 **إجمالي العملاء:** {len(results)} عميل"
                                    await update.message.reply_text(second_half, parse_mode='Markdown')
                                else:
                                    await update.message.reply_text(report, parse_mode='Markdown')
                            else:
                                await update.message.reply_text("❌ لا توجد بيانات للفترة المحددة")

                        elif waiting_for == 'days_areas':
                            # استعلام العملاء حسب المناطق
                            query = f"""
                            SELECT
                                city.NameAr as CityName,
                                COUNT(*) as CustomerCount
                            FROM Acc_Customers c
                            LEFT JOIN Sys_City city ON c.CityId = city.CityId
                            WHERE c.Status=1 AND c.IsDeleted=0 {branch_condition} {date_condition}
                            GROUP BY city.NameAr
                            ORDER BY CustomerCount DESC
                            """

                            cursor.execute(query, branch_params + date_params)
                            results = cursor.fetchall()

                            if results:
                                report = f"🌍 **عدد العملاء حسب المناطق في {branch_name} خلال {days_text}:**\n\n"
                                total = 0
                                for row in results:
                                    city_name = row[0] or 'غير محدد'
                                    count = row[1]
                                    total += count
                                    report += f"📍 {city_name}: {count:,} عميل\n"

                                report += f"\n📈 **إجمالي العملاء:** {total:,} عميل"
                                await update.message.reply_text(report, parse_mode='Markdown')
                            else:
                                await update.message.reply_text("❌ لا توجد بيانات للفترة المحددة")

                        elif waiting_for == 'days_social':
                            # استعلام العملاء حسب وسائل التواصل
                            query = f"""
                            SELECT
                                sm.NameAr as SocialMediaName,
                                COUNT(*) as CustomerCount
                            FROM Acc_Customers c
                            LEFT JOIN Sys_SocialMedia sm ON c.SocialMediaId = sm.SocialMediaId
                            WHERE c.Status=1 AND c.IsDeleted=0 {branch_condition} {date_condition}
                            GROUP BY sm.NameAr
                            ORDER BY CustomerCount DESC
                            """

                            cursor.execute(query, branch_params + date_params)
                            results = cursor.fetchall()

                            if results:
                                report = f"� **عدد العملاء حسب وسائل التواصل الاجتماعي في {branch_name} خلال {days_text}:**\n\n"
                                total = 0
                                for row in results:
                                    social_name = row[0] or 'غير محدد'
                                    count = row[1]
                                    total += count
                                    report += f"📲 {social_name}: {count:,} عميل\n"

                                report += f"\n📈 **إجمالي العملاء:** {total:,} عميل"
                                await update.message.reply_text(report, parse_mode='Markdown')
                            else:
                                await update.message.reply_text("❌ لا توجد بيانات للفترة المحددة")

                        conn.close()
                        print(f"✅ تم تنفيذ التقرير: {waiting_for} - {branch_name} - {days_text}")

                    except Exception as e:
                        await update.message.reply_text(f"❌ حدث خطأ أثناء تحضير التقرير:\n{str(e)}")
                        print(f"❌ خطأ في التقرير: {e}")

                    # إعادة تعيين الحالة
                    if context.user_data is not None:
                        context.user_data['waiting_for'] = None
                    return

                elif waiting_for == 'customer_search':
                    # التحقق من أوامر الخروج
                    if user_input.lower() in ['الغاء', 'إلغاء', 'خروج', 'cancel', '/start']:
                        if context.user_data is not None:
                            context.user_data['waiting_for'] = None
                        await update.message.reply_text("❌ تم إلغاء البحث. استخدم /start للعودة للقائمة الرئيسية.")
                        return

                    print(f"🔍 بحث عن عميل: {user_input} من: {user_name}")
                    await update.message.reply_text("🔄 جاري البحث...")

                    # البحث في قاعدة البيانات
                    try:
                        import pyodbc

                        # إعدادات الاتصال الجديدة
                        server = "alisamaraa.ddns.net,4100"
                        database = "Terra"
                        user_db = "sa"
                        password_db = "@a123admin4"

                        conn_str = f'DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={user_db};PWD={password_db}'

                        conn = pyodbc.connect(conn_str)
                        cursor = conn.cursor()

                        # أولاً، فحص بسيط للعميل
                        simple_query = """
                        SELECT CustomerCode, NameAr, Status, IsDeleted, BranchId
                        FROM Acc_Customers
                        WHERE CustomerCode = ? OR MainPhoneNo = ? OR SubMainPhoneNo = ?
                        """

                        cursor.execute(simple_query, (user_input, user_input, user_input))
                        simple_result = cursor.fetchone()

                        if simple_result:
                            print(f"🔍 عميل موجود: {simple_result[0]}, الحالة: {simple_result[2]}, محذوف: {simple_result[3]}")

                        # الاستعلام الكامل
                        query = """
                        SELECT TOP 1
                            c.CustomerCode, c.NameAr, c.NameEn, c.MainPhoneNo, c.SubMainPhoneNo,
                            c.Address, c.Email, c.NationalId, b.NameAr as BranchName,
                            city.NameAr as CityName, pt.NameAr as PayTypeName,
                            sm.NameAr as SocialMediaName, u.FullName as AddedBy, c.AddDate,
                            c.Status, c.IsDeleted
                        FROM Acc_Customers c
                        LEFT JOIN Sys_Branches b ON c.BranchId = b.BranchId
                        LEFT JOIN Sys_City city ON c.CityId = city.CityId
                        LEFT JOIN Acc_PayType pt ON c.PayTypeId = pt.PayTypeId
                        LEFT JOIN Sys_SocialMedia sm ON c.SocialMediaId = sm.SocialMediaId
                        LEFT JOIN Sys_Users u ON c.AddUser = u.UserId
                        WHERE (c.CustomerCode = ? OR c.MainPhoneNo = ? OR c.SubMainPhoneNo = ?)
                        """

                        cursor.execute(query, (user_input, user_input, user_input))
                        customer = cursor.fetchone()
                        conn.close()

                        if customer:
                            # التحقق من حالة العميل
                            status = customer[14] if len(customer) > 14 else 1
                            is_deleted = customer[15] if len(customer) > 15 else 0

                            if is_deleted == 1:
                                await update.message.reply_text(
                                    f"⚠️ **العميل {customer[0]} محذوف من النظام**\n\n"
                                    "🔄 من فضلك أدخل كود أو رقم هاتف آخر:",
                                    parse_mode='Markdown'
                                )
                                return
                            elif status != 1:
                                await update.message.reply_text(
                                    f"⚠️ **العميل {customer[0]} غير نشط**\n\n"
                                    "الحالة: غير نشط\n"
                                    "🔄 من فضلك أدخل كود أو رقم هاتف آخر:",
                                    parse_mode='Markdown'
                                )
                                return

                            customer_info = f"""📋 **بيانات العميل**

🆔 **كود العميل:** {customer[0] or 'غير محدد'}
🏢 **الفرع:** {customer[8] or 'غير محدد'}
👤 **الاسم (عربي):** {customer[1] or 'غير محدد'}
👤 **الاسم (إنجليزي):** {customer[2] or 'غير محدد'}
📞 **الهاتف الأساسي:** {customer[3] or 'غير محدد'}
📞 **الهاتف الفرعي:** {customer[4] or 'غير محدد'}
🏠 **العنوان:** {customer[5] or 'غير محدد'}
📧 **البريد الإلكتروني:** {customer[6] or 'غير محدد'}
🆔 **رقم الهوية:** {customer[7] or 'غير محدد'}
🌍 **المنطقة:** {customer[9] or 'غير محدد'}
💳 **نوع الدفع:** {customer[10] or 'غير محدد'}
📱 **وسيلة التواصل:** {customer[11] or 'غير محدد'}
👨‍💼 **أضيف بواسطة:** {customer[12] or 'غير محدد'}
📅 **تاريخ الإضافة:** {customer[13].strftime('%Y-%m-%d') if customer[13] else 'غير محدد'}
✅ **الحالة:** نشط"""

                            await update.message.reply_text(customer_info, parse_mode='Markdown')
                            print(f"✅ تم العثور على العميل: {customer[0]}")
                        else:
                            await update.message.reply_text(
                                "❌ **لم يتم العثور على العميل**\n\n"
                                "تأكد من:\n"
                                "• صحة كود العميل\n"
                                "• صحة رقم الهاتف\n"
                                "• أن العميل غير محذوف من النظام\n\n"
                                "🔄 **من فضلك أدخل كود أو رقم هاتف صحيح:**\n"
                                "أو اضغط /start للعودة للقائمة الرئيسية",
                                parse_mode='Markdown'
                            )
                            print(f"❌ لم يتم العثور على العميل: {user_input}")
                            # الاستمرار في انتظار إدخال جديد (عدم إعادة تعيين الحالة)
                            return

                        # إعادة تعيين الحالة فقط عند العثور على العميل
                        if context.user_data is not None:
                            context.user_data['waiting_for'] = None

                    except Exception as e:
                        await update.message.reply_text(f"❌ حدث خطأ أثناء البحث:\n{str(e)}")
                        print(f"❌ خطأ في البحث: {e}")
                        if context.user_data is not None:
                            context.user_data['waiting_for'] = None

        # دالة استعلام عن العملاء حسب الفروع
        async def customers_by_branch(update: Update, context: ContextTypes.DEFAULT_TYPE):
            if update.effective_user and update.message:
                user_name = update.effective_user.first_name or "مستخدم"
                print(f"📨 طلب استعلام عملاء حسب الفروع من: {user_name}")

                await update.message.reply_text(
                    "📍 **من فضلك اختر الفرع:**\n\n"
                    "1 - فرع التجمع\n"
                    "2 - فرع مدينة نصر\n"
                    "3 - للكل",
                    parse_mode='Markdown'
                )

                if context.user_data is not None:
                    context.user_data['waiting_for'] = 'branch_selection_customers'

        # دالة عدد العملاء حسب المناطق
        async def customers_by_areas(update: Update, context: ContextTypes.DEFAULT_TYPE):
            if update.effective_user and update.message:
                user_name = update.effective_user.first_name or "مستخدم"
                print(f"📨 طلب عدد العملاء حسب المناطق من: {user_name}")

                await update.message.reply_text(
                    "📍 **من فضلك اختر الفرع:**\n\n"
                    "1 - فرع التجمع\n"
                    "2 - فرع مدينة نصر\n"
                    "3 - للكل",
                    parse_mode='Markdown'
                )

                if context.user_data is not None:
                    context.user_data['waiting_for'] = 'branch_selection_areas'

        # دالة عدد عملاء وسائل التواصل
        async def customers_by_social_media(update: Update, context: ContextTypes.DEFAULT_TYPE):
            if update.effective_user and update.message:
                user_name = update.effective_user.first_name or "مستخدم"
                print(f"📨 طلب عدد عملاء وسائل التواصل من: {user_name}")

                await update.message.reply_text(
                    "📍 **من فضلك اختر الفرع:**\n\n"
                    "1 - فرع التجمع\n"
                    "2 - فرع مدينة نصر\n"
                    "3 - للكل",
                    parse_mode='Markdown'
                )

                if context.user_data is not None:
                    context.user_data['waiting_for'] = 'branch_selection_social'

        app = Application.builder().token(config['token']).build()

        # إضافة معالجات الأوامر
        app.add_handler(CommandHandler("start", start))
        app.add_handler(CommandHandler("test", test_db))

        # إضافة معالجات الأزرار والنصوص
        from telegram.ext import MessageHandler, filters
        app.add_handler(MessageHandler(filters.Regex("^🔍 بحث عميل$"), search_customer))
        app.add_handler(MessageHandler(filters.Regex("^📊 استعلام عن العملاء$"), customers_by_branch))
        app.add_handler(MessageHandler(filters.Regex("^🌍 عدد العملاء حسب المناطق$"), customers_by_areas))
        app.add_handler(MessageHandler(filters.Regex("^📱 عدد عملاء وسائل التواصل$"), customers_by_social_media))
        app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_text))

        print("✅ البوت جاهز للعمل!")
        print("📱 أرسل /start في التليجرام")
        print("⏹️ اضغط Ctrl+C لإيقاف البوت")
        print("=" * 50)

        # تشغيل البوت مع معالجة التعارض
        app.run_polling(
            drop_pending_updates=True,
            close_loop=False
        )

    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البوت")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
        logger.error(f"خطأ في تشغيل البوت: {e}")

        # إذا كان الخطأ بسبب تعارض البوتات
        if "Conflict" in str(e):
            print("\n💡 يبدو أن هناك بوت آخر يعمل!")
            print("🔧 شغل stop_all_bots.bat لإيقاف جميع البوتات")
            print("ثم أعد تشغيل البوت مرة أخرى")

if __name__ == "__main__":
    main()
