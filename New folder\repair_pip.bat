@echo off
title Repair pip and Install Packages
cls

echo Repairing pip and installing packages...
echo.

set PYTHON_PATH="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"

echo Step 1: Repair pip installation
%PYTHON_PATH% -m ensurepip --upgrade

echo.
echo Step 2: Upgrade pip
%PYTHON_PATH% -m pip install --upgrade pip

echo.
echo Step 3: Install required packages
%PYTHON_PATH% -m pip install python-telegram-bot
%PYTHON_PATH% -m pip install pyodbc
%PYTHON_PATH% -m pip install cryptography

echo.
echo Step 4: Test installations
echo Testing telegram...
%PYTHON_PATH% -c "import telegram; print('telegram: SUCCESS')" 2>nul || echo "telegram: FAILED"

echo Testing pyodbc...
%PYTHON_PATH% -c "import pyodbc; print('pyodbc: SUCCESS')" 2>nul || echo "pyodbc: FAILED"

echo Testing cryptography...
%PYTHON_PATH% -c "import cryptography; print('cryptography: SUCCESS')" 2>nul || echo "cryptography: FAILED"

echo.
echo All done! 
echo.
echo VS Code Configuration:
echo 1. Press Ctrl+Shift+P in VS Code
echo 2. Type: Python: Select Interpreter  
echo 3. Select: %PYTHON_PATH%
echo 4. Press Ctrl+Shift+P again
echo 5. Type: Developer: Reload Window
echo.

pause
