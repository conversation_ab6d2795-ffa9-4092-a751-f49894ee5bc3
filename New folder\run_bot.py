#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل بوت TeERA
"""

import sys
import traceback

def main():
    try:
        print("🚀 بدء تشغيل بوت TeERA...")
        
        # استيراد البوت
        from terra_bot_enhanced import main as bot_main
        
        # تشغيل البوت
        bot_main()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("تأكد من تثبيت المكتبات المطلوبة:")
        print("pip install python-telegram-bot cryptography pyodbc")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
        print("\nتفاصيل الخطأ:")
        traceback.print_exc()
        
    finally:
        print("\n👋 تم إنهاء البوت")

if __name__ == "__main__":
    main()
