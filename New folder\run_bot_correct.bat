@echo off
title TeERA Bot - Correct Python
cd /d "%~dp0"
cls

echo ================================================
echo           TeERA Bot Starting...
echo ================================================
echo.

set PYTHON_EXE="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"

echo Using Python: %PYTHON_EXE%
%PYTHON_EXE% --version
echo Current directory: %CD%
echo.

if exist simple_working_bot.py (
    echo Starting simple_working_bot.py...
    echo.
    %PYTHON_EXE% simple_working_bot.py
) else if exist final_bot.py (
    echo Starting final_bot.py...
    echo.
    %PYTHON_EXE% final_bot.py
) else (
    echo Error: No bot files found!
    echo Available Python files:
    dir *.py /b
)

echo.
echo Bot stopped.
pause
