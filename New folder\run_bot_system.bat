@echo off
chcp 65001 >nul
title TeERA Bot - System Python
cls

echo ================================================
echo           🤖 TeERA Bot 🤖
echo ================================================
echo.
echo Starting bot with system Python...
echo.

REM Use system Python directly
C:\Python\python.exe final_bot.py

REM If that doesn't work, try common Python paths
if %errorlevel% neq 0 (
    echo Trying alternative Python path...
    C:\Python39\python.exe final_bot.py
)

if %errorlevel% neq 0 (
    echo Trying Python from PATH...
    python final_bot.py
)

if %errorlevel% neq 0 (
    echo Trying py launcher...
    py final_bot.py
)

echo.
echo Bot stopped. Press any key to exit...
pause >nul
