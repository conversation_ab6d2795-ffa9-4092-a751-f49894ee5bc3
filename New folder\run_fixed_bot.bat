@echo off
title TeERA Bot - Fixed Version
cd /d "%~dp0"
cls

echo ================================================
echo           TeERA Bot - Fixed Version
echo ================================================
echo.

set PYTHON_EXE="C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe"

echo Using Python: %PYTHON_EXE%
echo Current directory: %CD%
echo.

if exist terra_bot_fixed.py (
    echo Starting fixed TeERA Bot...
    echo.
    %PYTHON_EXE% terra_bot_fixed.py
) else (
    echo Error: terra_bot_fixed.py not found!
    echo Available Python files:
    dir *.py /b
)

echo.
echo Bot stopped.
pause
