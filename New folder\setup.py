#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعداد بوت TeERA
"""

import os
import sys
from cryptography.fernet import Fernet

def create_encryption_files():
    """إنشاء ملفات التشفير"""
    try:
        # إنشاء مفتاح التشفير
        key = Fernet.generate_key()
        with open('encryption_key.key', 'wb') as key_file:
            key_file.write(key)

        # تشفير التوكن
        token = "7556046481:AAEMttgLYzrMu1q2ogRlgsV8mgscqZKbbcY"
        fernet = Fernet(key)
        encrypted_token = fernet.encrypt(token.encode())

        with open('encrypted_token.txt', 'wb') as token_file:
            token_file.write(encrypted_token)

        print("✅ تم إنشاء ملفات التشفير بنجاح!")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملفات التشفير: {e}")
        return False

def check_files():
    """التحقق من وجود الملفات المطلوبة"""
    required_files = [
        'db_credentials.txt',
        'CREDINTEL.txt'
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    return missing_files

def create_missing_files():
    """إنشاء الملفات المفقودة"""
    
    # إنشاء db_credentials.txt
    if not os.path.exists('db_credentials.txt'):
        with open('db_credentials.txt', 'w', encoding='utf-8') as f:
            f.write("SERVER=hej08pxktvw.sn.mynetname.net\n")
            f.write("USER=sa\n")
            f.write("PASSWORD=Ret_ME@\n")
        print("✅ تم إنشاء ملف db_credentials.txt")
    
    # إنشاء CREDINTEL.txt
    if not os.path.exists('CREDINTEL.txt'):
        with open('CREDINTEL.txt', 'w', encoding='utf-8') as f:
            f.write("token:7556046481:AAEMttgLYzrMu1q2ogRlgsV8mgscqZKbbcY\n")
            f.write("SERVER=hej08pxktvw.sn.mynetname.net\n")
            f.write("USER=sa\n")
            f.write("PASSWORD=Ret_ME@\n")
        print("✅ تم إنشاء ملف CREDINTEL.txt")

def install_requirements():
    """تثبيت المكتبات المطلوبة"""
    try:
        import subprocess
        print("📦 جاري تثبيت المكتبات المطلوبة...")
        
        packages = [
            "python-telegram-bot==20.7",
            "cryptography==41.0.7", 
            "pyodbc==4.0.39"
        ]
        
        for package in packages:
            print(f"تثبيت {package}...")
            result = subprocess.run([sys.executable, "-m", "pip", "install", package], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                print(f"❌ فشل في تثبيت {package}")
                return False
        
        print("✅ تم تثبيت جميع المكتبات بنجاح!")
        return True
    except Exception as e:
        print(f"❌ خطأ في تثبيت المكتبات: {e}")
        return False

def test_imports():
    """اختبار استيراد المكتبات"""
    try:
        import telegram
        import pyodbc
        import cryptography
        print("✅ جميع المكتبات متاحة")
        return True
    except ImportError as e:
        print(f"❌ مكتبة مفقودة: {e}")
        return False

def main():
    """الدالة الرئيسية للإعداد"""
    print("🔧 إعداد بوت TeERA")
    print("=" * 50)
    
    # التحقق من الملفات المفقودة
    missing_files = check_files()
    if missing_files:
        print(f"📁 ملفات مفقودة: {', '.join(missing_files)}")
        print("🔨 جاري إنشاء الملفات المفقودة...")
        create_missing_files()
    else:
        print("✅ جميع ملفات الإعداد موجودة")
    
    # اختبار المكتبات
    if not test_imports():
        print("📦 جاري تثبيت المكتبات...")
        if not install_requirements():
            print("❌ فشل في الإعداد")
            return False
    
    # إنشاء ملفات التشفير
    if not os.path.exists('encryption_key.key') or not os.path.exists('encrypted_token.txt'):
        print("🔐 جاري إنشاء ملفات التشفير...")
        if not create_encryption_files():
            print("⚠️ فشل في إنشاء ملفات التشفير، سيتم استخدام التوكن مباشرة")
    else:
        print("✅ ملفات التشفير موجودة")
    
    print("\n🎉 تم الإعداد بنجاح!")
    print("\nيمكنك الآن تشغيل البوت باستخدام:")
    print("- python terra_bot_enhanced.py")
    print("- python test_bot.py")
    print("- start_bot.bat (على Windows)")
    
    return True

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 تم إلغاء الإعداد")
    except Exception as e:
        print(f"\n❌ خطأ في الإعداد: {e}")
        import traceback
        traceback.print_exc()
