# استعلام العملاء المبسط
def get_customers_simple(branch_condition, date_condition, branch_params, date_params):
    """استعلام مبسط للعملاء"""
    
    import pyodbc
    
    # إعدادات الاتصال
    server = "alisamaraa.ddns.net,4100"
    database = "Terra"
    user = "sa"
    password = "@a123admin4"
    
    conn_str = f'DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={user};PWD={password}'
    
    try:
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        # استعلام مبسط - 20 عميل فقط
        query = f"""
        SELECT TOP 20
            c.CustomerCode,
            c.NameAr,
            c.MainPhoneNo,
            b.NameAr as BranchName
        FROM Acc_Customers c
        LEFT JOIN Sys_Branches b ON c.BranchId = b.BranchId
        WHERE c.Status=1 AND c.IsDeleted=0 {branch_condition} {date_condition}
        ORDER BY c.AddDate DESC
        """
        
        cursor.execute(query, branch_params + date_params)
        results = cursor.fetchall()
        conn.close()
        
        if results:
            report = "📊 **قائمة العملاء:**\n\n"
            
            for i, row in enumerate(results, 1):
                customer_code = row[0] or 'غير محدد'
                customer_name = row[1] or 'غير محدد'
                phone = row[2] or 'غير محدد'
                branch_name = row[3] or 'غير محدد'
                
                report += f"{i}. 🆔 `{customer_code}` - {customer_name}\n"
                report += f"📞 `{phone}` | 🏢 {branch_name}\n\n"
            
            report += f"📈 **إجمالي:** {len(results)} عميل"
            return report
        else:
            return "❌ لا توجد بيانات للفترة المحددة"
            
    except Exception as e:
        return f"❌ خطأ في الاستعلام: {str(e)}"

if __name__ == "__main__":
    # اختبار
    result = get_customers_simple("", "", [], [])
    print(result)
