#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🔄 بدء تشغيل بوت TeERA...")

try:
    from telegram import Update
    from telegram.ext import Application, CommandHandler, ContextTypes
    print("✅ تم تحميل مكتبات التليجرام بنجاح")
except ImportError as e:
    print(f"❌ خطأ في تحميل المكتبات: {e}")
    print("💡 تأكد من تثبيت: pip install python-telegram-bot")
    input("اضغط Enter للخروج...")
    exit(1)

# التوكن
TOKEN = "7556046481:AAEMttgLYzrMu1q2ogRlgsV8mgscqZKbbcY"

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """دالة البداية"""
    user_name = update.effective_user.first_name
    print(f"📨 استقبال رسالة /start من: {user_name}")
    
    await update.message.reply_text(
        f"🎉 مرحباً {user_name}!\n\n"
        "✅ بوت TeERA يعمل بنجاح!\n"
        "🚀 استخدم /test للاختبار"
    )

async def test(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """دالة الاختبار"""
    user_name = update.effective_user.first_name
    print(f"📨 استقبال رسالة /test من: {user_name}")
    
    await update.message.reply_text("✅ البوت يعمل بشكل ممتاز! 🚀")

def main():
    """الدالة الرئيسية"""
    print("🔄 إنشاء تطبيق البوت...")
    
    try:
        # إنشاء التطبيق
        app = Application.builder().token(TOKEN).build()
        
        # إضافة المعالجات
        app.add_handler(CommandHandler("start", start))
        app.add_handler(CommandHandler("test", test))
        
        print("✅ تم إعداد البوت بنجاح")
        print("🚀 البوت يعمل الآن...")
        print("📱 أرسل /start في التليجرام")
        print("⏹️ اضغط Ctrl+C لإيقاف البوت")
        print("=" * 40)
        
        # تشغيل البوت
        app.run_polling(drop_pending_updates=True)
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
