@echo off
chcp 65001 >nul
title TeERA Bot

echo.
echo ========================================
echo           🤖 TeERA Bot 🤖
echo ========================================
echo.

echo 🔄 جاري التحقق من المكتبات المطلوبة...
python -c "import telegram, pyodbc, cryptography" 2>nul
if errorlevel 1 (
    echo ❌ بعض المكتبات غير مثبتة
    echo 📦 جاري تثبيت المكتبات...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ فشل في تثبيت المكتبات
        pause
        exit /b 1
    )
)

echo ✅ المكتبات جاهزة

echo.
echo اختر نوع التشغيل:
echo 1. البوت المحسن (مستحسن)
echo 2. البوت الأصلي
echo 3. اختبار البوت
echo 4. إنشاء ملفات التشفير
echo.

set /p choice="أدخل اختيارك (1-4): "

if "%choice%"=="1" (
    echo 🚀 تشغيل البوت المحسن...
    python terra_bot_enhanced.py
) else if "%choice%"=="2" (
    echo 🚀 تشغيل البوت الأصلي...
    python "boot finish33 - Copy.py"
) else if "%choice%"=="3" (
    echo 🧪 تشغيل اختبار البوت...
    python test_bot.py
) else if "%choice%"=="4" (
    echo 🔐 إنشاء ملفات التشفير...
    python create_encrypted_token.py
    echo ✅ تم إنشاء ملفات التشفير
    pause
) else (
    echo ❌ اختيار غير صحيح
    pause
    goto :eof
)

echo.
echo 👋 تم إنهاء البوت
pause
