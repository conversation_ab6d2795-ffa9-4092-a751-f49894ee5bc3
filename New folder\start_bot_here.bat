@echo off
title TeERA Bot
cd /d "%~dp0"
cls

echo ================================================
echo           TeERA Bot Starting...
echo ================================================
echo.
echo Current directory: %CD%
echo.

REM Check if Python files exist
if exist simple_working_bot.py (
    echo Found simple_working_bot.py - Starting bot...
    echo.
    python simple_working_bot.py
) else if exist terra_bot_enhanced.py (
    echo Found terra_bot_enhanced.py - Starting bot...
    echo.
    python terra_bot_enhanced.py
) else if exist final_bot.py (
    echo Found final_bot.py - Starting bot...
    echo.
    python final_bot.py
) else (
    echo Error: No bot files found!
    echo.
    echo Available Python files in this directory:
    dir *.py /b
    echo.
    echo Please make sure you have one of these files:
    echo - simple_working_bot.py
    echo - terra_bot_enhanced.py  
    echo - final_bot.py
)

echo.
echo Bot stopped.
pause
