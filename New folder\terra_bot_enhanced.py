#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TeERA Bot - Enhanced Version
بوت تليجرام للتعامل مع قاعدة بيانات Terra
"""

import os
import pyodbc
import asyncio
from cryptography.fernet import Fernet
from telegram import Update, ReplyKeyboardMarkup, KeyboardButton, BotCommand, ReplyKeyboardRemove
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes, ConversationHandler
import logging
from datetime import datetime

# إعداد الـ logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('terra_bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Constants
DOWNLOAD_FILES, SELECT_BRANCH, SELECT_DAYS, SEARCH_CUSTOMER, SEARCH_EMPLOYEES, GENERAL_INQUIRY = range(6)
LOGIN_USERNAME, LOGIN_PASSWORD = range(2)

BRANCHES = {
    "1": {"name": "التجمع", "id": 3},
    "2": {"name": "مدينة نصر", "id": 2}
}

class TerraBot:
    def __init__(self):
        self.token = None
        self.db_credentials = {}
        self.load_config()

    def load_config(self):
        """تحميل إعدادات البوت"""
        # تحميل التوكن
        self.token = self.get_bot_token()

        # تحميل بيانات قاعدة البيانات
        self.db_credentials = self.read_db_credentials()

        logger.info("تم تحميل إعدادات البوت بنجاح")

    def get_bot_token(self):
        """الحصول على توكن البوت"""
        try:
            # محاولة فك التشفير أولاً
            with open('encryption_key.key', 'rb') as key_file:
                key = key_file.read()
            fernet = Fernet(key)

            with open('encrypted_token.txt', 'rb') as token_file:
                encrypted_token = token_file.read()

            return fernet.decrypt(encrypted_token).decode()
        except Exception as e:
            logger.warning(f"فشل فك التشفير: {e}")
            # استخدام التوكن من ملف CREDINTEL.txt
            try:
                with open('CREDINTEL.txt', 'r', encoding='utf-8') as file:
                    for line in file:
                        if line.startswith('token:'):
                            return line.split(':', 1)[1].strip()
            except Exception as e2:
                logger.error(f"فشل قراءة التوكن: {e2}")
            return None

    def read_db_credentials(self):
        """قراءة بيانات الاتصال بقاعدة البيانات"""
        credentials = {}
        try:
            with open('db_credentials.txt', 'r', encoding='utf-8') as file:
                for line in file:
                    if '=' in line:
                        key, value = line.strip().split('=', 1)
                        credentials[key] = value
        except FileNotFoundError:
            logger.error("ملف db_credentials.txt غير موجود")
        return credentials

    def connect_to_db(self):
        """الاتصال بقاعدة البيانات"""
        try:
            server = self.db_credentials.get('SERVER', '')
            database = 'Terra'
            user = self.db_credentials.get('USER')
            password = self.db_credentials.get('PASSWORD')

            if user == '0' and password == '0':
                conn_str = f'DRIVER={{SQL Server}};SERVER={server};DATABASE={database};Trusted_Connection=yes'
            else:
                conn_str = f'DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={user};PWD={password}'

            conn = pyodbc.connect(conn_str)
            logger.info("تم الاتصال بقاعدة البيانات بنجاح")
            return conn
        except Exception as e:
            logger.error(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return None

# إنشاء مثيل من البوت
bot_instance = TerraBot()

async def set_bot_commands(application):
    """تعيين أوامر البوت"""
    commands = [
        BotCommand("start", "بدء البوت"),
        BotCommand("run", "تشغيل البوت"),
        BotCommand("test", "اختبار الاتصال"),
        BotCommand("help", "المساعدة"),
    ]
    await application.bot.set_my_commands(commands)
    logger.info("تم تعيين أوامر البوت")

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """دالة البداية"""
    keyboard = [
        [KeyboardButton("بحث عميل"), KeyboardButton("استعلام عن العملاء")],
        [KeyboardButton("عدد عملاء وسائل التواصل"), KeyboardButton("عدد العملاء حسب المناطق")],
        [KeyboardButton("بحث موظف"), KeyboardButton("تحميل ملفات موظف")],
        [KeyboardButton("تحميل معاينات"), KeyboardButton("تحميل اجتماعات")],
        [KeyboardButton("إنهاء المحادثة")],
    ]

    reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=False, resize_keyboard=True)

    welcome_msg = f"""
🌟 مرحباً بك في بوت TeERA! 🌟

📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}
👤 المستخدم: {update.effective_user.first_name}

اختر أحد الخيارات التالية:
    """

    await update.message.reply_text(welcome_msg, reply_markup=reply_markup)
    logger.info(f"بدء جلسة جديدة للمستخدم: {update.effective_user.id}")

async def test_connection(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """اختبار الاتصال بقاعدة البيانات"""
    await update.message.reply_text("🔄 جاري اختبار الاتصال...")

    try:
        conn = bot_instance.connect_to_db()
        if conn:
            cursor = conn.cursor()

            # اختبار بسيط
            cursor.execute("SELECT COUNT(*) FROM Acc_Customers WHERE IsDeleted=0")
            customer_count = cursor.fetchone()[0]

            cursor.execute("SELECT COUNT(*) FROM Emp_Employees WHERE IsDeleted=0")
            employee_count = cursor.fetchone()[0]

            conn.close()

            success_msg = f"""
✅ تم الاتصال بقاعدة البيانات بنجاح!

📊 إحصائيات سريعة:
👥 عدد العملاء: {customer_count:,}
👨‍💼 عدد الموظفين: {employee_count:,}
🕐 وقت الاختبار: {datetime.now().strftime('%H:%M:%S')}
            """
            await update.message.reply_text(success_msg)
        else:
            await update.message.reply_text("❌ فشل الاتصال بقاعدة البيانات")
    except Exception as e:
        error_msg = f"❌ خطأ في الاتصال:\n{str(e)}"
        await update.message.reply_text(error_msg)
        logger.error(f"خطأ في اختبار الاتصال: {e}")

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """عرض المساعدة"""
    help_text = """
🆘 مساعدة بوت TeERA

🔹 الأوامر المتاحة:
/start - بدء البوت
/test - اختبار الاتصال بقاعدة البيانات
/help - عرض هذه المساعدة

🔹 الوظائف الرئيسية:
• بحث عميل - البحث بالكود أو رقم الهاتف
• بحث موظف - البحث عن بيانات الموظفين
• تحميل معاينات - تحميل ملفات معاينات العملاء
• تحميل اجتماعات - تحميل ملفات الاجتماعات
• استعلامات إحصائية - عدد العملاء حسب المناطق ووسائل التواصل

📞 للدعم الفني: اتصل بالإدارة
    """
    await update.message.reply_text(help_text)

# دوال البحث عن العملاء
async def search_customer_start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """بداية البحث عن عميل"""
    await update.message.reply_text(
        "🔍 البحث عن عميل\n\n"
        "من فضلك أدخل:\n"
        "• كود العميل\n"
        "• رقم الهاتف الأساسي\n"
        "• رقم الهاتف الفرعي"
    )
    return SEARCH_CUSTOMER

async def search_customer(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """البحث عن عميل"""
    search_term = update.message.text.strip()

    if search_term == "إنهاء المحادثة":
        await cancel(update, context)
        return ConversationHandler.END

    if not search_term:
        await update.message.reply_text("⚠️ النص المدخل فارغ. من فضلك أدخل كود العميل أو رقم الهاتف.")
        return SEARCH_CUSTOMER

    await update.message.reply_text("🔄 جاري البحث...")

    try:
        conn = bot_instance.connect_to_db()
        if not conn:
            await update.message.reply_text("❌ تعذر الاتصال بقاعدة البيانات.")
            return ConversationHandler.END

        cursor = conn.cursor()
        query = """
        SELECT TOP 1
            c.CustomerCode, c.NameAr, c.NameEn, c.MainPhoneNo, c.SubMainPhoneNo,
            c.Address, c.Email, c.NationalId, b.NameAr as BranchName,
            city.NameAr as CityName, pt.NameAr as PayTypeName,
            sm.NameAr as SocialMediaName, u.FullName as AddedBy, c.AddDate
        FROM Acc_Customers c
        LEFT JOIN Sys_Branches b ON c.BranchId = b.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Acc_PayType pt ON c.PayTypeId = pt.PayTypeId
        LEFT JOIN Sys_SocialMedia sm ON c.SocialMediaId = sm.SocialMediaId
        LEFT JOIN Sys_Users u ON c.AddUser = u.UserId
        WHERE c.Status=1 AND c.IsDeleted=0
        AND (c.CustomerCode = ? OR c.MainPhoneNo = ? OR c.SubMainPhoneNo = ?)
        """
        cursor.execute(query, (search_term, search_term, search_term))
        customer = cursor.fetchone()
        conn.close()

        if customer:
            customer_info = f"""
📋 **بيانات العميل**

🆔 **كود العميل:** {customer[0] or 'غير محدد'}
🏢 **الفرع:** {customer[8] or 'غير محدد'}
👤 **الاسم (عربي):** {customer[1] or 'غير محدد'}
👤 **الاسم (إنجليزي):** {customer[2] or 'غير محدد'}
📞 **الهاتف الأساسي:** {customer[3] or 'غير محدد'}
📞 **الهاتف الفرعي:** {customer[4] or 'غير محدد'}
🏠 **العنوان:** {customer[5] or 'غير محدد'}
📧 **البريد الإلكتروني:** {customer[6] or 'غير محدد'}
🆔 **رقم الهوية:** {customer[7] or 'غير محدد'}
🌍 **المنطقة:** {customer[9] or 'غير محدد'}
💳 **نوع الدفع:** {customer[10] or 'غير محدد'}
📱 **وسيلة التواصل:** {customer[11] or 'غير محدد'}
👨‍💼 **أضيف بواسطة:** {customer[12] or 'غير محدد'}
📅 **تاريخ الإضافة:** {customer[13].strftime('%Y-%m-%d') if customer[13] else 'غير محدد'}
            """
            await update.message.reply_text(customer_info, parse_mode='Markdown')
            await update.message.reply_text("🔍 للبحث عن عميل آخر، أدخل كود أو رقم هاتف جديد:")
            return SEARCH_CUSTOMER
        else:
            await update.message.reply_text(
                "❌ لم يتم العثور على العميل.\n\n"
                "تأكد من:\n"
                "• صحة كود العميل\n"
                "• صحة رقم الهاتف\n"
                "• أن العميل غير محذوف من النظام"
            )
            return SEARCH_CUSTOMER

    except Exception as e:
        error_msg = f"❌ حدث خطأ أثناء البحث:\n{str(e)}"
        await update.message.reply_text(error_msg)
        logger.error(f"خطأ في البحث عن العميل: {e}")
        return ConversationHandler.END

async def cancel(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """إنهاء المحادثة"""
    await update.message.reply_text(
        "❌ تم إنهاء المحادثة.\n\n"
        "استخدم /start للعودة إلى القائمة الرئيسية."
    )
    return ConversationHandler.END

def main():
    """الدالة الرئيسية"""
    print("🔄 بدء تشغيل بوت TeERA...")

    if not bot_instance.token:
        print("❌ لم يتم العثور على توكن البوت")
        logger.error("لم يتم العثور على توكن البوت")
        return

    print(f"✅ تم تحميل التوكن: {bot_instance.token[:10]}...")
    logger.info("بدء تشغيل بوت TeERA...")

    # إنشاء التطبيق
    print("🔄 إنشاء تطبيق التليجرام...")
    application = Application.builder().token(bot_instance.token).build()

    # تعيين أوامر البوت سيتم تلقائياً عند بدء التشغيل
    print("🔄 إضافة معالجات الأوامر...")

    # إضافة المعالجات
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("test", test_connection))
    application.add_handler(CommandHandler("help", help_command))

    # معالج البحث عن العملاء
    search_handler = ConversationHandler(
        entry_points=[MessageHandler(filters.Regex("^بحث عميل$"), search_customer_start)],
        states={SEARCH_CUSTOMER: [MessageHandler(filters.TEXT & ~filters.COMMAND, search_customer)]},
        fallbacks=[MessageHandler(filters.Regex("^إنهاء المحادثة$"), cancel)],
    )
    application.add_handler(search_handler)

    # معالج إنهاء المحادثة
    application.add_handler(MessageHandler(filters.Regex("^إنهاء المحادثة$"), cancel))

    print("✅ تم إعداد جميع المعالجات")
    logger.info("البوت جاهز للعمل!")
    print("🚀 بوت TeERA يعمل الآن...")
    print("📱 أرسل /start في التليجرام لبدء استخدام البوت")
    print("⏹️ اضغط Ctrl+C لإيقاف البوت")

    # تشغيل البوت
    try:
        print("🔄 بدء استقبال الرسائل...")
        application.run_polling(drop_pending_updates=True)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البوت بواسطة المستخدم")
        logger.info("تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
        logger.error(f"خطأ في تشغيل البوت: {e}")

if __name__ == "__main__":
    main()