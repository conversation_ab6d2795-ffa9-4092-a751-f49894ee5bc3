import os
import pyodbc
import asyncio
from telegram import Update, ReplyKeyboardMarkup, KeyboardButton, BotCommand
from telegram import ReplyKeyboardRemove
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes, ConversationHandler
import logging

# إعداد الـ logging
logging.basicConfig(
    filename="bot_errors.log",
    level=logging.ERROR,
    format="%(asctime)s - %(levelname)s - %(message)s"
)

# Constants
DOWNLOAD_FILES, SELECT_BRANCH, SELECT_DAYS, SEARCH_CUSTOMER, SEARCH_EMPLOYEES, GENERAL_INQUIRY = range(6)
BRANCHES = {"1": "التجمع", "2": "مدينة نصر"}
LOGIN_USERNAME, LOGIN_PASSWORD = range(2)

# Token مباشرة للاختبار
BOT_TOKEN = "7556046481:AAEMttgLYzrMu1q2ogRlgsV8mgscqZKbbcY"

def read_credentials():
    """قراءة بيانات الاتصال بقاعدة البيانات"""
    credentials = {}
    try:
        with open('db_credentials.txt', 'r') as file:
            for line in file:
                if '=' in line:
                    key, value = line.strip().split('=', 1)
                    credentials[key] = value
    except FileNotFoundError:
        print("ملف db_credentials.txt غير موجود")
    return credentials

def connect_to_db():
    """الاتصال بقاعدة البيانات"""
    credentials = read_credentials()
    server = credentials.get('SERVER', '') 
    database = 'Terra'
    user = credentials.get('USER')
    password = credentials.get('PASSWORD')
    
    try:
        if user == '0' and password == '0':
            conn = pyodbc.connect(f'DRIVER={{SQL Server}};SERVER={server};DATABASE={database};Trusted_Connection=yes')
        else:
            conn = pyodbc.connect(f'DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={user};PWD={password}')
        return conn
    except Exception as e:
        print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

async def set_bot_commands(application):
    """تعيين أوامر البوت"""
    commands = [
        BotCommand("start", "بدء البوت"),
        BotCommand("run", "تشغيل البوت"),
        BotCommand("stop", "إيقاف البوت"),
    ]
    await application.bot.set_my_commands(commands)

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """دالة البداية"""
    keyboard = [
        [KeyboardButton("بحث عميل"), KeyboardButton("استعلام عن العملاء")],
        [KeyboardButton("عدد عملاء وسائل التواصل"), KeyboardButton("عدد العملاء حسب المناطق")],
        [KeyboardButton("بحث موظف"), KeyboardButton("تحميل ملفات موظف")],
        [KeyboardButton("تحميل معاينات"), KeyboardButton("تحميل اجتماعات")],
        [KeyboardButton("إنهاء المحادثة")],
    ]
    
    reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=False)
    await update.message.reply_text("مرحباً! اختر أحد الخيارات التالية:", reply_markup=reply_markup)

async def search_customer_start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """بداية البحث عن عميل"""
    await update.message.reply_text("من فضلك أدخل كود العميل أو رقم الهاتف للبحث:")
    return SEARCH_CUSTOMER

async def search_customer(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """البحث عن عميل"""
    search_term = update.message.text.strip()

    if search_term == "إنهاء المحادثة":
        await cancel(update, context)
        return ConversationHandler.END

    if not search_term:
        await update.message.reply_text("النص المدخل فارغ. من فضلك أدخل كود العميل أو رقم الهاتف.")
        return SEARCH_CUSTOMER

    try:
        conn = connect_to_db()
        if not conn:
            await update.message.reply_text("⚠️ تعذر الاتصال بقاعدة البيانات.")
            return ConversationHandler.END
            
        cursor = conn.cursor()
        query = """
        SELECT TOP 1 CustomerCode, NameAr, MainPhoneNo, SubMainPhoneNo, Address, Email
        FROM Acc_Customers 
        WHERE Status=1 AND IsDeleted=0 
        AND (CustomerCode = ? OR MainPhoneNo = ? OR SubMainPhoneNo = ?)
        """
        cursor.execute(query, (search_term, search_term, search_term))
        customer = cursor.fetchone()
        conn.close()

        if customer:
            customer_info = f"""
📋 بيانات العميل:
كود العميل: {customer[0]}
الاسم: {customer[1]}
الهاتف الأساسي: {customer[2]}
الهاتف الفرعي: {customer[3]}
العنوان: {customer[4]}
البريد الإلكتروني: {customer[5]}
            """
            await update.message.reply_text(customer_info)
            await update.message.reply_text("للبحث عن عميل آخر برجاء كتابة كود أو رقم الهاتف")
            return SEARCH_CUSTOMER
        else:
            await update.message.reply_text("لم يتم العثور على العميل باستخدام هذا الكود أو الرقم.")
            return SEARCH_CUSTOMER

    except Exception as e:
        await update.message.reply_text(f"حدث خطأ أثناء البحث: {str(e)}")
        return ConversationHandler.END

async def cancel(update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
    """إنهاء المحادثة"""
    await update.message.reply_text("❌ تم إنهاء المحادثة.")
    await start(update, context)
    return ConversationHandler.END

async def test_db_connection(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        conn = connect_to_db()
        if conn:
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM Acc_Customers WHERE IsDeleted=0")
            count = cursor.fetchone()[0]
            conn.close()
            await update.message.reply_text(f"✅ تم الاتصال بقاعدة البيانات بنجاح!\nعدد العملاء: {count}")
        else:
            await update.message.reply_text("❌ فشل الاتصال بقاعدة البيانات")
    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في الاتصال: {str(e)}")

def main():
    """الدالة الرئيسية"""
    print("بدء تشغيل البوت...")
    
    # إنشاء التطبيق
    application = Application.builder().token(BOT_TOKEN).build()
    
    # إضافة المعالجات
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("test", test_db_connection))
    
    # معالج البحث عن العملاء
    search_handler = ConversationHandler(
        entry_points=[MessageHandler(filters.Regex("^بحث عميل$"), search_customer_start)],
        states={SEARCH_CUSTOMER: [MessageHandler(filters.TEXT & ~filters.COMMAND, search_customer)]},
        fallbacks=[MessageHandler(filters.Regex("^إنهاء المحادثة$"), cancel)],
    )
    application.add_handler(search_handler)
    
    # معالج إنهاء المحادثة
    application.add_handler(MessageHandler(filters.Regex("^إنهاء المحادثة$"), cancel))
    
    print("البوت جاهز للعمل!")
    
    # تشغيل البوت
    application.run_polling()

if __name__ == "__main__":
    main()
