#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار بسيط لبوت TeERA
"""

import logging
from telegram import Update, ReplyKeyboardMarkup, KeyboardButton
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes

# إعداد الـ logging
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

# التوكن
BOT_TOKEN = "7556046481:AAEMttgLYzrMu1q2ogRlgsV8mgscqZKbbcY"

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """دالة البداية"""
    keyboard = [
        [KeyboardButton("اختبار الاتصال"), KeyboardButton("معلومات البوت")],
        [KeyboardButton("مساعدة")],
    ]
    
    reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=False, resize_keyboard=True)
    
    welcome_msg = """
🌟 مرحباً بك في بوت TeERA! 🌟

هذا اختبار بسيط للبوت.
اختر أحد الخيارات أدناه:
    """
    
    await update.message.reply_text(welcome_msg, reply_markup=reply_markup)

async def test_connection(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """اختبار الاتصال"""
    await update.message.reply_text("✅ البوت يعمل بشكل طبيعي!")

async def bot_info(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معلومات البوت"""
    info = """
🤖 معلومات البوت:
- الاسم: TeERA Bot
- الإصدار: 1.0
- الحالة: نشط ✅
    """
    await update.message.reply_text(info)

async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """المساعدة"""
    help_text = """
🆘 مساعدة البوت:

الأوامر المتاحة:
/start - بدء البوت
/help - عرض المساعدة

الأزرار:
• اختبار الاتصال - للتأكد من عمل البوت
• معلومات البوت - عرض معلومات البوت
• مساعدة - عرض هذه الرسالة
    """
    await update.message.reply_text(help_text)

async def handle_text(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """معالجة النصوص"""
    text = update.message.text
    
    if text == "اختبار الاتصال":
        await test_connection(update, context)
    elif text == "معلومات البوت":
        await bot_info(update, context)
    elif text == "مساعدة":
        await help_command(update, context)
    else:
        await update.message.reply_text(f"تم استلام رسالتك: {text}")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل بوت TeERA (اختبار)...")
    
    # إنشاء التطبيق
    application = Application.builder().token(BOT_TOKEN).build()
    
    # إضافة المعالجات
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("help", help_command))
    application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_text))
    
    print("✅ البوت جاهز للعمل!")
    print("اضغط Ctrl+C لإيقاف البوت")
    
    # تشغيل البوت
    try:
        application.run_polling(drop_pending_updates=True)
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف البوت")
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    main()
