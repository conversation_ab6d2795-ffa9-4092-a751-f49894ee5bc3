#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
from datetime import datetime

print("=" * 50)
print("🐍 Python Environment Test")
print("=" * 50)
print(f"📍 Python Version: {sys.version}")
print(f"📍 Python Path: {sys.executable}")
print(f"📍 Current Directory: {os.getcwd()}")
print(f"📍 Current Time: {datetime.now()}")
print("=" * 50)

# Test imports
print("\n🔍 Testing imports...")

try:
    import telegram
    print("✅ telegram - OK")
except ImportError:
    print("❌ telegram - Missing")

try:
    import pyodbc
    print("✅ pyodbc - OK")
except ImportError:
    print("❌ pyodbc - Missing")

try:
    import cryptography
    print("✅ cryptography - OK")
except ImportError:
    print("❌ cryptography - Missing")

print("\n" + "=" * 50)
print("💡 If any packages are missing, run:")
print("   pip install python-telegram-bot pyodbc cryptography")
print("=" * 50)

input("\nPress Enter to exit...")
