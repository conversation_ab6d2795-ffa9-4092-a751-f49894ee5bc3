#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بوت تليجرام بسيط للاختبار
"""

import logging
from telegram import Update
from telegram.ext import Application, CommandHandler, ContextTypes

# إعداد الـ logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# التوكن
TOKEN = "7556046481:AAEMttgLYzrMu1q2ogRlgsV8mgscqZKbbcY"

async def start(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """دالة البداية"""
    print(f"📨 تم استقبال رسالة /start من المستخدم: {update.effective_user.id}")
    await update.message.reply_text("🎉 مرحباً! البوت يعمل بنجاح!")

async def test(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """دالة الاختبار"""
    print(f"📨 تم استقبال رسالة /test من المستخدم: {update.effective_user.id}")
    await update.message.reply_text("✅ البوت يعمل بشكل صحيح!")

def main():
    """الدالة الرئيسية"""
    print("🔄 بدء تشغيل البوت البسيط...")
    
    # إنشاء التطبيق
    print("🔄 إنشاء تطبيق التليجرام...")
    application = Application.builder().token(TOKEN).build()
    
    # إضافة المعالجات
    print("🔄 إضافة معالجات الأوامر...")
    application.add_handler(CommandHandler("start", start))
    application.add_handler(CommandHandler("test", test))
    
    print("✅ تم إعداد البوت")
    print("🚀 البوت يعمل الآن...")
    print("📱 أرسل /start أو /test في التليجرام")
    print("⏹️ اضغط Ctrl+C لإيقاف البوت")
    
    # تشغيل البوت
    try:
        print("🔄 بدء استقبال الرسائل...")
        application.run_polling(drop_pending_updates=True)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البوت")
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    main()
