#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🔍 Testing VS Code Python Environment Fix...")
print("=" * 50)

# Test imports
success_count = 0
total_packages = 3

try:
    import telegram
    print("✅ telegram - OK")
    success_count += 1
except ImportError as e:
    print(f"❌ telegram - Error: {e}")

try:
    import pyodbc
    print("✅ pyodbc - OK")
    success_count += 1
except ImportError as e:
    print(f"❌ pyodbc - Error: {e}")

try:
    from cryptography.fernet import Fernet
    print("✅ cryptography - OK")
    success_count += 1
except ImportError as e:
    print(f"❌ cryptography - Error: {e}")

print("=" * 50)

if success_count == total_packages:
    print("🎉 جميع المكتبات تعمل بنجاح!")
    print("✅ VS Code تم إصلاحه بنجاح")
    print("🚀 يمكنك الآن تشغيل البوت")
else:
    print(f"⚠️ {total_packages - success_count} مكتبات لا تزال مفقودة")
    print("💡 جرب تشغيل fix_vscode.bat مرة أخرى")

print("=" * 50)
