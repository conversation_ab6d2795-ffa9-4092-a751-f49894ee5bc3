# تحديث القائمة الرئيسية
def update_main_menu():
    keyboard = [
        [KeyboardButton("🔍 بحث عميل"), KeyboardButton("📊 استعلام عن العملاء")],
        [KeyboardButton("📱 عدد عملاء وسائل التواصل"), KeyboardButton("🌍 عدد العملاء حسب المناطق")],
        [KeyboardButton("📊 عملاء بدون معاينات"), KeyboardButton("👁️ معاينات بدون اجتماعات")],
        [KeyboardButton("🤝 اجتماعات بدون تصميمات"), KeyboardButton("🎨 تصميمات بدون عقود")],
        [KeyboardButton("❌ إنهاء المحادثة")],
    ]
    return keyboard

# تحديث معالجات الأزرار
def update_handlers():
    handlers = [
        ("^📊 عملاء بدون معاينات$", "customers_without_previews_start"),
        ("^👁️ معاينات بدون اجتماعات$", "previews_without_meetings_start"),
        ("^🤝 اجتماعات بدون تصميمات$", "meetings_without_designs_start"),
        ("^🎨 تصميمات بدون عقود$", "designs_without_contracts_start"),
    ]
    return handlers

# تحديث قائمة الأزرار في is_menu_button
def update_menu_buttons():
    menu_buttons = [
        '🔍 بحث عميل', '📊 استعلام عن العملاء', '📱 عدد عملاء وسائل التواصل', 
        '🌍 عدد العملاء حسب المناطق', '📊 عملاء بدون معاينات',
        '👁️ معاينات بدون اجتماعات', '🤝 اجتماعات بدون تصميمات', 
        '🎨 تصميمات بدون عقود', '👨‍💼 بحث موظف',
        '📁 تحميل ملفات موظف', '🤝 تحميل اجتماعات', '❌ إنهاء المحادثة',
        '🔙 العودة للقائمة الرئيسية', 'نعم - عرض التفاصيل', 'لا - العودة للقائمة'
    ]
    return menu_buttons
