#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TeERA Bot - Clean Version
بوت تليجرام نظيف ومحسن للتعامل مع قاعدة بيانات Terra
"""

import logging
import asyncio
import os
from datetime import datetime

# مكتبات PDF
try:
    from reportlab.lib.pagesizes import A4
    from reportlab.lib import colors
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    from bidi.algorithm import get_display
    import arabic_reshaper
    PDF_AVAILABLE = True
except ImportError:
    PDF_AVAILABLE = False
    print("⚠️ مكتبات PDF غير متوفرة. سيتم تعطيل ميزة تصدير PDF.")

# إعداد الـ logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('terra_bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'server': 'alisamaraa.ddns.net,4100',
    'database': 'Terra',
    'user': 'sa',
    'password': '@a123admin4'
}

# توكن البوت
BOT_TOKEN = "7556046481:AAEMttgLYzrMu1q2ogRlgsV8mgscqZKbbcY"

def connect_to_db():
    """الاتصال بقاعدة البيانات"""
    try:
        import pyodbc
        
        conn_str = f"DRIVER={{SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['user']};PWD={DB_CONFIG['password']}"
        
        conn = pyodbc.connect(conn_str)
        logger.info("تم الاتصال بقاعدة البيانات بنجاح")
        return conn
    except Exception as e:
        logger.error(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

def check_table_names():
    """فحص أسماء الجداول والأعمدة المتاحة"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات"

        cursor = conn.cursor()

        result = "📋 فحص الجداول والأعمدة:\n\n"

        # فحص جدول Sys_Previews
        try:
            cursor.execute("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Sys_Previews'")
            preview_columns = [col[0] for col in cursor.fetchall()]
            result += f"Sys_Previews أعمدة: {', '.join(preview_columns)}\n\n"
        except Exception as e:
            result += f"خطأ في Sys_Previews: {str(e)}\n\n"

        # فحص جدول Sys_Designs
        try:
            cursor.execute("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Sys_Designs'")
            design_columns = [col[0] for col in cursor.fetchall()]
            result += f"Sys_Designs أعمدة: {', '.join(design_columns)}\n\n"
        except Exception as e:
            result += f"خطأ في Sys_Designs: {str(e)}\n\n"

        # فحص جدول Acc_Contracts
        try:
            cursor.execute("SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Acc_Contracts'")
            contract_columns = [col[0] for col in cursor.fetchall()]
            result += f"Acc_Contracts أعمدة: {', '.join(contract_columns)}\n\n"
        except Exception as e:
            result += f"خطأ في Acc_Contracts: {str(e)}\n\n"

        conn.close()
        return result

    except Exception as e:
        return f"❌ خطأ في فحص الجداول: {str(e)}"

def test_database():
    """اختبار قاعدة البيانات"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ فشل الاتصال بقاعدة البيانات"
        
        cursor = conn.cursor()
        
        # فحص الفروع
        cursor.execute("SELECT BranchId, NameAr FROM Sys_Branches")
        branches = cursor.fetchall()
        
        # === إحصائيات العملاء ===
        # إجمالي العملاء (كل الحالات)
        cursor.execute("SELECT COUNT(*) FROM Acc_Customers")
        result = cursor.fetchone()
        total_all_customers = result[0] if result else 0

        # العملاء غير المحذوفين (كل الحالات)
        cursor.execute("SELECT COUNT(*) FROM Acc_Customers WHERE IsDeleted=0")
        result = cursor.fetchone()
        non_deleted_customers = result[0] if result else 0

        # العملاء النشطين (Status=1 وغير محذوفين)
        cursor.execute("SELECT COUNT(*) FROM Acc_Customers WHERE Status=1 AND IsDeleted=0")
        result = cursor.fetchone()
        active_customers = result[0] if result else 0

        # العملاء غير النشطين (Status!=1 وغير محذوفين)
        cursor.execute("SELECT COUNT(*) FROM Acc_Customers WHERE Status!=1 AND IsDeleted=0")
        result = cursor.fetchone()
        inactive_customers = result[0] if result else 0

        # العملاء المحذوفين
        cursor.execute("SELECT COUNT(*) FROM Acc_Customers WHERE IsDeleted=1")
        result = cursor.fetchone()
        deleted_customers = result[0] if result else 0

        # العملاء اللي عندهم معاينات (كل الحالات - نشطة ومحذوفة)
        cursor.execute("""
        SELECT COUNT(DISTINCT CustomerId)
        FROM Sys_Previews
        """)
        result = cursor.fetchone()
        customers_with_any_previews = result[0] if result else 0

        # العملاء اللي عندهم معاينات نشطة فقط
        cursor.execute("""
        SELECT COUNT(DISTINCT CustomerId)
        FROM Sys_Previews
        WHERE IsDeleted = 0
        """)
        result = cursor.fetchone()
        customers_with_active_previews_only = result[0] if result else 0

        # العملاء المحذوفين اللي عندهم معاينات
        cursor.execute("""
        SELECT COUNT(DISTINCT c.CustomerCode)
        FROM Acc_Customers c
        INNER JOIN Sys_Previews p ON c.CustomerCode = p.CustomerId
        WHERE c.IsDeleted = 1
        """)
        result = cursor.fetchone()
        deleted_customers_with_previews = result[0] if result else 0

        # العملاء غير النشطين اللي عندهم معاينات (أي معاينات - نشطة أو محذوفة)
        cursor.execute("""
        SELECT COUNT(DISTINCT c.CustomerCode)
        FROM Acc_Customers c
        INNER JOIN Sys_Previews p ON c.CustomerCode = p.CustomerId
        WHERE c.Status != 1 AND c.IsDeleted = 0
        """)
        result = cursor.fetchone()
        inactive_customers_with_previews = result[0] if result else 0

        # العملاء النشطين اللي عندهم معاينات (أي معاينات - نشطة أو محذوفة)
        cursor.execute("""
        SELECT COUNT(DISTINCT c.CustomerCode)
        FROM Acc_Customers c
        INNER JOIN Sys_Previews p ON c.CustomerCode = p.CustomerId
        WHERE c.Status = 1 AND c.IsDeleted = 0
        """)
        result = cursor.fetchone()
        active_customers_with_previews = result[0] if result else 0

        # العملاء اللي عندهم معاينات لكن مش موجودين في جدول العملاء أو محذوفين
        cursor.execute("""
        SELECT COUNT(DISTINCT p.CustomerId)
        FROM Sys_Previews p
        LEFT JOIN Acc_Customers c ON p.CustomerId = c.CustomerCode
        WHERE c.CustomerCode IS NULL OR c.IsDeleted = 1
        """)
        result = cursor.fetchone()
        unlinked_or_deleted_customers_with_previews = result[0] if result else 0

        # === إحصائيات المعاينات ===
        # إجمالي المعاينات (كل الحالات)
        cursor.execute("SELECT COUNT(*) FROM Sys_Previews")
        result = cursor.fetchone()
        total_all_previews = result[0] if result else 0

        # المعاينات النشطة (غير محذوفة)
        cursor.execute("SELECT COUNT(*) FROM Sys_Previews WHERE IsDeleted=0")
        result = cursor.fetchone()
        active_previews = result[0] if result else 0

        # المعاينات المحذوفة
        cursor.execute("SELECT COUNT(*) FROM Sys_Previews WHERE IsDeleted=1")
        result = cursor.fetchone()
        deleted_previews = result[0] if result else 0

        # المعاينات المحولة (PreviewConvert IS NOT NULL)
        cursor.execute("SELECT COUNT(*) FROM Sys_Previews WHERE PreviewConvert IS NOT NULL AND IsDeleted=0")
        result = cursor.fetchone()
        converted_previews = result[0] if result else 0

        # المعاينات غير المحولة (PreviewConvert IS NULL)
        cursor.execute("SELECT COUNT(*) FROM Sys_Previews WHERE PreviewConvert IS NULL AND IsDeleted=0")
        result = cursor.fetchone()
        not_converted_previews = result[0] if result else 0

        conn.close()
        
        # تكوين التقرير
        report = f"""🔧 **معلومات الخادم وقاعدة البيانات:**

🖥️ **اسم الخادم:** `{DB_CONFIG['server']}`
🗄️ **اسم قاعدة البيانات:** `{DB_CONFIG['database']}`
👤 **المستخدم:** `{DB_CONFIG['user']}`

🏢 **الفروع المتاحة:**
"""
        
        for branch in branches:
            report += f"• {branch[1]} (ID: {branch[0]})\n"
        
        # === تقرير شامل ومفصل ===
        report += f"\n\n📊 **=== إحصائيات العملاء ===**"
        report += f"\n👥 **إجمالي العملاء (كل الحالات):** {total_all_customers:,} عميل"
        report += f"\n✅ **العملاء النشطين (غير محذوفين):** {active_customers:,} عميل"
        report += f"\n⏸️ **العملاء غير النشطين (غير محذوفين):** {inactive_customers:,} عميل"
        report += f"\n🗑️ **العملاء المحذوفين:** {deleted_customers:,} عميل"

        report += f"\n\n🔢 **=== توزيع المعاينات على العملاء ===**"
        report += f"\n📈 **إجمالي العملاء اللي عملتلهم معاينة:** {customers_with_any_previews:,} عميل"
        report += f"\n✅ **عملاء نشطين لسه عندهم معاينات:** {active_customers_with_previews:,} عميل"
        report += f"\n⏸️ **عملاء غير نشطين لسه عندهم معاينات:** {inactive_customers_with_previews:,} عميل"
        report += f"\n❌ **عملاء محذوفين لديهم معاينات:** {deleted_customers_with_previews:,} عميل"

        # حساب العملاء اللي معايناتهم اتحولت
        customers_with_converted_previews = customers_with_any_previews - (active_customers_with_previews + inactive_customers_with_previews + deleted_customers_with_previews)
        if customers_with_converted_previews > 0:
            report += f"\n🔄 **عملاء معايناتهم اتحولت أو اتمحت:** {customers_with_converted_previews:,} عميل"

        # حساب العملاء اللي محتاجين معاينة (من العملاء النشطين)
        # العملاء النشطين اللي عملتلهم معاينة = العملاء النشطين اللي لسه عندهم معاينات + اللي معايناتهم اتحولت
        active_customers_had_previews = active_customers_with_previews + customers_with_converted_previews
        customers_need_previews = active_customers - active_customers_had_previews

        report += f"\n\n📋 **=== العملاء المحتاجين معاينة ===**"
        report += f"\n🎯 **عملاء نشطين محتاجين معاينة:** {customers_need_previews:,} عميل"
        report += f"\n✅ **عملاء نشطين عملتلهم معاينة خلاص:** {active_customers_had_previews:,} عميل"

        report += f"\n\n👁️ **=== إحصائيات المعاينات ===**"
        report += f"\n📈 **إجمالي المعاينات (كل الحالات):** {total_all_previews:,} معاينة"
        report += f"\n✅ **المعاينات النشطة (غير محذوفة):** {active_previews:,} معاينة"
        report += f"\n🗑️ **المعاينات المحذوفة:** {deleted_previews:,} معاينة"
        report += f"\n🔄 **المعاينات المحولة:** {converted_previews:,} معاينة"
        report += f"\n⏳ **المعاينات غير المحولة:** {not_converted_previews:,} معاينة"

        return report
        
    except Exception as e:
        return f"❌ خطأ في فحص قاعدة البيانات: {str(e)}"

def search_customer_in_db(search_term):
    """البحث عن عميل في قاعدة البيانات"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات"
        
        cursor = conn.cursor()
        
        query = """
        SELECT TOP 1
            c.CustomerCode, c.NameAr, c.NameEn, c.MainPhoneNo, c.SubMainPhoneNo,
            c.Address, c.Email, c.NationalId, b.NameAr as BranchName,
            city.NameAr as CityName, pt.NameAr as PayTypeName,
            sm.NameAr as SocialMediaName, u.FullName as AddedBy, c.AddDate,
            c.Status, c.IsDeleted
        FROM Acc_Customers c
        LEFT JOIN Sys_Branches b ON c.BranchId = b.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Acc_PayType pt ON c.PayTypeId = pt.PayTypeId
        LEFT JOIN Sys_SocialMedia sm ON c.SocialMediaId = sm.SocialMediaId
        LEFT JOIN Sys_Users u ON c.AddUser = u.UserId
        WHERE (c.CustomerCode = ? OR c.MainPhoneNo = ? OR c.SubMainPhoneNo = ?)
        """
        
        cursor.execute(query, (search_term, search_term, search_term))
        customer = cursor.fetchone()
        conn.close()
        
        if customer:
            # التحقق من حالة العميل
            status = customer[14] if len(customer) > 14 else 1
            is_deleted = customer[15] if len(customer) > 15 else 0
            
            if is_deleted == 1:
                return f"⚠️ **العميل {customer[0]} محذوف من النظام**\n\n🔄 من فضلك أدخل كود أو رقم هاتف آخر:"
            elif status != 1:
                return f"⚠️ **العميل {customer[0]} غير نشط**\n\nالحالة: غير نشط\n🔄 من فضلك أدخل كود أو رقم هاتف آخر:"
            
            # تنظيف البيانات من الرموز الخاصة
            def clean_text(text):
                if text is None:
                    return 'غير محدد'
                # إزالة الرموز التي قد تتعارض مع Markdown
                return str(text).replace('*', '').replace('_', '').replace('[', '').replace(']', '').replace('`', '')

            customer_info = f"""📋 بيانات العميل

كود العميل: {clean_text(customer[0])}
🏢 الفرع: {clean_text(customer[8])}
👤 الاسم (عربي): {clean_text(customer[1])}
👤 الاسم (إنجليزي): {clean_text(customer[2])}
📞 الهاتف الأساسي: {clean_text(customer[3])}
📞 الهاتف الفرعي: {clean_text(customer[4])}
🏠 العنوان: {clean_text(customer[5])}
📧 البريد الإلكتروني: {clean_text(customer[6])}
رقم الهوية: {clean_text(customer[7])}
🌍 المنطقة: {clean_text(customer[9])}
💳 نوع الدفع: {clean_text(customer[10])}
📱 وسيلة التواصل: {clean_text(customer[11])}
👨‍💼 أضيف بواسطة: {clean_text(customer[12])}
📅 تاريخ الإضافة: {customer[13].strftime('%Y-%m-%d') if customer[13] else 'غير محدد'}
✅ الحالة: نشط"""
            
            return customer_info
        else:
            return """❌ **لم يتم العثور على العميل**

تأكد من:
• صحة كود العميل
• صحة رقم الهاتف
• أن العميل غير محذوف من النظام

🔄 **من فضلك أدخل كود أو رقم هاتف صحيح:**
أو اضغط /start للعودة للقائمة الرئيسية"""
            
    except Exception as e:
        return f"❌ حدث خطأ أثناء البحث:\n{str(e)}"

def test_preview_table_structure():
    """اختبار بنية جدول المعاينات"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات"

        cursor = conn.cursor()

        # اختبار استعلام بسيط لمعرفة الأعمدة
        try:
            cursor.execute("SELECT TOP 1 * FROM Sys_Previews")
            columns = [column[0] for column in cursor.description]
            result = f"أعمدة Sys_Previews: {', '.join(columns)}\n"

            # اختبار بيانات عينة
            row = cursor.fetchone()
            if row:
                result += f"مثال على البيانات: {dict(zip(columns, row))}\n"

        except Exception as e:
            result = f"خطأ في Sys_Previews: {str(e)}\n"

        # اختبار جدول التصميمات
        try:
            cursor.execute("SELECT TOP 1 * FROM Sys_Designs")
            columns = [column[0] for column in cursor.description]
            result += f"أعمدة Sys_Designs: {', '.join(columns)}\n"

        except Exception as e:
            result += f"خطأ في Sys_Designs: {str(e)}\n"

        # اختبار جدول العقود
        try:
            cursor.execute("SELECT TOP 1 * FROM Acc_Contracts")
            columns = [column[0] for column in cursor.description]
            result += f"أعمدة Acc_Contracts: {', '.join(columns)}\n"

        except Exception as e:
            result += f"خطأ في Acc_Contracts: {str(e)}\n"

        # البحث عن جدول الاجتماعات
        try:
            cursor.execute("""
            SELECT TABLE_NAME
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_NAME LIKE '%meeting%' OR TABLE_NAME LIKE '%Meeting%'
            """)
            meeting_tables = cursor.fetchall()
            result += f"جداول الاجتماعات: {[table[0] for table in meeting_tables]}\n"

            # إذا وجد جدول اجتماعات، اعرض أعمدته
            if meeting_tables:
                table_name = meeting_tables[0][0]
                cursor.execute(f"SELECT TOP 1 * FROM {table_name}")
                columns = [column[0] for column in cursor.description]
                result += f"أعمدة {table_name}: {', '.join(columns)}\n"

        except Exception as e:
            result += f"خطأ في البحث عن جداول الاجتماعات: {str(e)}\n"

        conn.close()
        return result

    except Exception as e:
        return f"❌ خطأ في اختبار الجداول: {str(e)}"

def get_detailed_customer_statistics(branch_name, days_text):
    """الحصول على الإحصائيات المفصلة للعملاء"""
    try:
        conn = connect_to_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # تحديد شرط الفرع
        if 'للكل' in branch_name:
            branch_condition = ""
            branch_params = []
        elif 'التجمع' in branch_name:
            branch_condition = "AND c.BranchId = 3"
            branch_params = []
        elif 'مدينة نصر' in branch_name:
            branch_condition = "AND c.BranchId = 2"
            branch_params = []
        else:
            branch_condition = ""
            branch_params = []

        # تحديد شرط التاريخ
        if 'للكل' in days_text:
            date_condition = ""
            date_params = []
        else:
            try:
                days = int(days_text.replace(' ايام', ''))
                date_condition = "AND c.AddDate >= DATEADD(day, -?, GETDATE())"
                date_params = [days]
            except:
                date_condition = ""
                date_params = []

        # إجمالي العملاء
        query_total = f"""
        SELECT COUNT(*) FROM Acc_Customers c
        WHERE c.IsDeleted = 0 {branch_condition} {date_condition}
        """
        cursor.execute(query_total, branch_params + date_params)
        result = cursor.fetchone()
        total_customers = result[0] if result else 0

        # العملاء النشطين
        query_active = f"""
        SELECT COUNT(*) FROM Acc_Customers c
        WHERE c.Status = 1 AND c.IsDeleted = 0 {branch_condition} {date_condition}
        """
        cursor.execute(query_active, branch_params + date_params)
        result = cursor.fetchone()
        active_customers = result[0] if result else 0

        # العملاء المتوقفين
        inactive_customers = total_customers - active_customers

        # العملاء الذين لديهم معاينات (كل الحالات - نشطة ومحذوفة)
        query_with_previews = f"""
        SELECT COUNT(DISTINCT c.CustomerCode)
        FROM Acc_Customers c
        INNER JOIN Sys_Previews p ON c.CustomerCode = p.CustomerId
        WHERE c.Status = 1 AND c.IsDeleted = 0 {branch_condition}
        """
        cursor.execute(query_with_previews, branch_params)
        result = cursor.fetchone()
        customers_with_previews = result[0] if result else 0

        # العملاء بدون معاينات (بشكل عام، مش مقيد بالفترة)
        query_without_previews = f"""
        SELECT COUNT(DISTINCT c.CustomerCode)
        FROM Acc_Customers c
        LEFT JOIN Sys_Previews p ON c.CustomerCode = p.CustomerId AND p.IsDeleted = 0
        WHERE c.Status=1 AND c.IsDeleted=0
        AND p.CustomerId IS NULL {branch_condition}
        """
        cursor.execute(query_without_previews, branch_params)
        result = cursor.fetchone()
        customers_without_previews = result[0] if result else 0

        # إجمالي المعاينات (في الفترة والفرع المحددين)
        query_total_previews = f"""
        SELECT COUNT(*)
        FROM Sys_Previews p
        INNER JOIN Acc_Customers c ON p.CustomerId = c.CustomerCode
        WHERE p.IsDeleted = 0 AND c.Status = 1 AND c.IsDeleted = 0 {branch_condition} {date_condition}
        """
        cursor.execute(query_total_previews, branch_params + date_params)
        result = cursor.fetchone()
        total_previews = result[0] if result else 0

        conn.close()

        # حساب النسب
        # ملاحظة: العملاء مع/بدون معاينات محسوبين بشكل عام (مش مقيد بالفترة)
        # إجمالي المعاينات محسوب للفترة والفرع المحددين فقط

        # حساب العملاء بدون معاينات الصحيح
        customers_without_previews_calculated = active_customers - customers_with_previews

        stats_data = []
        if total_customers > 0:
            stats_data.append(("إجمالي العملاء", total_customers, 100.0))
            stats_data.append(("العملاء النشطين", active_customers, (active_customers/total_customers)*100))
            stats_data.append(("العملاء المتوقفين", inactive_customers, (inactive_customers/total_customers)*100))
            stats_data.append(("عملاء لديهم معاينات", customers_with_previews, (customers_with_previews/active_customers)*100 if active_customers > 0 else 0))
            stats_data.append(("عملاء بدون معاينات", customers_without_previews_calculated, (customers_without_previews_calculated/active_customers)*100 if active_customers > 0 else 0))
            stats_data.append(("إجمالي المعاينات", total_previews, 0))  # بدون نسبة للمعاينات

        return stats_data

    except Exception as e:
        print(f"❌ خطأ في الإحصائيات: {str(e)}")
        return None

def create_customers_pdf(customers_data, report_title, branch_name, days_text):
    """إنشاء ملف PDF للعملاء مع دعم العربية"""
    if not PDF_AVAILABLE:
        return None, "❌ مكتبات PDF غير متوفرة. شغل install_pdf_libraries.bat أولاً"

    try:
        # إنشاء اسم الملف
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"customers_report_{timestamp}.pdf"
        filepath = os.path.join("reports", filename)

        # إنشاء مجلد التقارير إذا لم يكن موجوداً
        os.makedirs("reports", exist_ok=True)

        # تسجيل خط يدعم العربية (إذا كان متوفراً)
        try:
            # محاولة استخدام خط النظام الذي يدعم العربية
            pdfmetrics.registerFont(TTFont('Arabic', 'C:/Windows/Fonts/arial.ttf'))
            arabic_font = 'Arabic'
        except:
            try:
                # محاولة أخرى مع خط آخر
                pdfmetrics.registerFont(TTFont('Arabic', 'C:/Windows/Fonts/tahoma.ttf'))
                arabic_font = 'Arabic'
            except:
                # استخدام الخط الافتراضي
                arabic_font = 'Helvetica'

        # إنشاء المستند
        doc = SimpleDocTemplate(filepath, pagesize=A4, rightMargin=72, leftMargin=72, topMargin=72, bottomMargin=18)

        # قائمة العناصر
        story = []

        # الأنماط
        styles = getSampleStyleSheet()

        # نمط العنوان الرئيسي
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1,  # وسط
            textColor=colors.darkblue,
            fontName='Helvetica-Bold'
        )

        # نمط العنوان الفرعي
        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=20,
            alignment=1,  # وسط
            textColor=colors.grey,
            fontName=arabic_font
        )

        # العنوان الرئيسي
        title_text = "تقرير العملاء بدون معاينات - مفصل"
        if arabic_font != 'Helvetica':
            title_text = str(arabic_reshaper.reshape(title_text))
            title_text = str(get_display(title_text))
        else:
            title_text = "Customers Without Previews - Detailed Report"
        story.append(Paragraph(title_text, title_style))

        # معلومات التقرير
        info_text = f"الفرع: {branch_name} | الفترة: {days_text} | التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        if arabic_font != 'Helvetica':
            info_text = str(arabic_reshaper.reshape(info_text))
            info_text = str(get_display(info_text))
        else:
            info_text = f"Branch: {branch_name} | Period: {days_text} | Date: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        story.append(Paragraph(info_text, subtitle_style))

        story.append(Spacer(1, 20))

        # إضافة الإحصائيات المفصلة
        stats_data = get_detailed_customer_statistics(branch_name, days_text)
        if stats_data:
            # عنوان الإحصائيات
            stats_title = "الإحصائيات التفصيلية"
            if arabic_font != 'Helvetica':
                stats_title = str(get_display(arabic_reshaper.reshape(stats_title)))
            else:
                stats_title = "Detailed Statistics"

            stats_title_style = ParagraphStyle(
                'StatsTitle',
                parent=subtitle_style,
                fontSize=14,
                textColor=colors.darkgreen,
                fontName=arabic_font
            )
            story.append(Paragraph(stats_title, stats_title_style))
            story.append(Spacer(1, 10))

            # جدول الإحصائيات
            stats_headers = ['البيان', 'العدد', 'النسبة']
            if arabic_font != 'Helvetica':
                stats_headers = [str(get_display(arabic_reshaper.reshape(h))) for h in stats_headers]
            else:
                stats_headers = ['Description', 'Count', 'Percentage']

            stats_table_data = [stats_headers]

            for stat_name, count, percentage in stats_data:
                if arabic_font != 'Helvetica':
                    stat_name = str(get_display(arabic_reshaper.reshape(stat_name)))

                # عرض النسبة فقط للبيانات التي لها نسبة
                if percentage > 0:
                    stats_table_data.append([stat_name, f"{count:,}", f"{percentage:.1f}%"])
                else:
                    stats_table_data.append([stat_name, f"{count:,}", "-"])

            stats_table = Table(stats_table_data, colWidths=[3*inch, 1*inch, 1*inch])
            stats_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkgreen),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, -1), arabic_font),
                ('FONTSIZE', (0, 0), (-1, 0), 11),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('BACKGROUND', (0, 1), (-1, -1), colors.lightgreen),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ]))

            story.append(stats_table)
            story.append(Spacer(1, 20))

        # إنشاء الجدول
        if customers_data:
            # رؤوس الجدول بالعربية
            headers = ['م', 'اسم العميل', 'رقم الهاتف', 'الفرع', 'التاريخ', 'الحالة']
            if arabic_font != 'Helvetica':
                headers = [str(get_display(arabic_reshaper.reshape(h))) for h in headers]

            # تحضير البيانات
            table_data = [headers]

            for i, customer in enumerate(customers_data, 1):  # كل العملاء
                # استخدام الأسماء العربية
                name = customer.get('name', 'غير محدد')
                phone = customer.get('phone', 'غير محدد')
                branch = customer.get('branch', 'غير محدد')
                date = customer.get('date', 'غير محدد')
                status = customer.get('status', 'غير محدد')

                # تطبيق معالجة العربية إذا كان الخط يدعمها
                if arabic_font != 'Helvetica':
                    if name and name != 'غير محدد':
                        name = str(get_display(arabic_reshaper.reshape(str(name))))
                    if branch and branch != 'غير محدد':
                        branch = str(get_display(arabic_reshaper.reshape(str(branch))))
                    if status and status != 'غير محدد':
                        status = str(get_display(arabic_reshaper.reshape(str(status))))

                row = [
                    str(i),
                    name,  # اسم العميل العربي
                    phone,
                    branch,  # اسم الفرع العربي
                    date,
                    status  # حالة العميل
                ]
                table_data.append(row)

            # إنشاء الجدول
            table = Table(table_data, colWidths=[0.8*inch, 2.5*inch, 1.5*inch, 1.5*inch, 1.2*inch])

            # تنسيق الجدول
            table.setStyle(TableStyle([
                # رأس الجدول
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), arabic_font),
                ('FONTSIZE', (0, 0), (-1, 0), 12),

                # محتوى الجدول
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
                ('FONTNAME', (0, 1), (-1, -1), arabic_font),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),

                # تناوب الألوان
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey]),
            ]))

            story.append(table)

            # إحصائيات بالعربية
            story.append(Spacer(1, 20))
            stats_text = f"إجمالي العملاء في التقرير: {len(customers_data)} عميل"

            if arabic_font != 'Helvetica':
                stats_text = str(get_display(arabic_reshaper.reshape(stats_text)))

            story.append(Paragraph(stats_text, subtitle_style))

        # بناء المستند
        doc.build(story)

        return filepath, f"✅ تم إنشاء ملف PDF: {filename}"

    except Exception as e:
        return None, f"❌ خطأ في إنشاء PDF: {str(e)}"

def get_customers_without_previews_for_pdf(branch_id, days):
    """الحصول على بيانات العملاء بدون معاينات لتصدير PDF"""
    try:
        conn = connect_to_db()
        if not conn:
            return [], "❌ تعذر الاتصال بقاعدة البيانات"

        cursor = conn.cursor()

        # تحديد شرط الفرع
        if branch_id == '3':  # للكل
            branch_condition = ""
            branch_params = []
        else:
            actual_branch_id = '3' if branch_id == '1' else '2'  # التجمع=3, مدينة نصر=2
            branch_condition = "AND c.BranchId = ?"
            branch_params = [actual_branch_id]

        # تحديد شرط التاريخ
        if days is None:  # للكل
            date_condition = ""
            date_params = []
        else:
            date_condition = "AND c.AddDate >= DATEADD(day, -?, GETDATE())"
            date_params = [days]

        query = f"""
        SELECT
            c.CustomerCode,
            c.NameAr,
            c.MainPhoneNo,
            b.NameAr as BranchName,
            c.AddDate,
            c.Status
        FROM Acc_Customers c
        LEFT JOIN Sys_Branches b ON c.BranchId = b.BranchId
        LEFT JOIN Sys_Previews p ON c.CustomerCode = p.CustomerId
        WHERE c.Status = 1 AND c.IsDeleted = 0
        AND p.CustomerId IS NULL {branch_condition} {date_condition}
        ORDER BY c.AddDate DESC, c.CustomerCode ASC
        """

        cursor.execute(query, branch_params + date_params)
        results = cursor.fetchall()
        conn.close()

        if results:
            customers_data = []
            for customer_code, name, phone, branch, add_date, status in results:
                # تحديد حالة العميل
                status_text = "نشط" if status == 1 else f"غير نشط ({status})"

                customers_data.append({
                    'code': customer_code or 'غير محدد',
                    'name': name or 'غير محدد',
                    'phone': phone or 'غير محدد',
                    'branch': branch or 'غير محدد',
                    'date': add_date.strftime('%Y-%m-%d') if add_date else 'غير محدد',
                    'status': status_text
                })

            return customers_data, "✅ تم تحضير البيانات بنجاح"
        else:
            return [], "❌ لا توجد بيانات للفترة المحددة"

    except Exception as e:
        return [], f"❌ حدث خطأ أثناء تحضير البيانات: {str(e)}"

def get_customers_with_previews_no_meetings(branch_id, days):
    """الحصول على العملاء الذين لهم معاينات بدون اجتماعات"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات"

        cursor = conn.cursor()

        # تحديد شرط الفرع
        if branch_id == '3':  # للكل
            branch_condition = ""
            branch_params = []
        else:
            actual_branch_id = '3' if branch_id == '1' else '2'  # التجمع=3, مدينة نصر=2
            branch_condition = "AND p.BranchId = ?"
            branch_params = [actual_branch_id]

        # تحديد شرط التاريخ
        if days is None:  # للكل
            date_condition = ""
            date_params = []
        else:
            date_condition = "AND p.Date >= DATEADD(day, -?, GETDATE())"
            date_params = [days]

        query = f"""
        SELECT
            p.PreviewCode,
            c.NameAr,
            c.MainPhoneNo,
            b.NameAr as BranchName,
            p.Date as PreviewDate
        FROM Sys_Previews p
        INNER JOIN Acc_Customers c ON p.CustomerId = c.CustomerCode
        LEFT JOIN Sys_Branches b ON p.BranchId = b.BranchId
        WHERE p.IsDeleted = 0 AND c.Status = 1 AND c.IsDeleted = 0
        AND p.MeetingId IS NULL {branch_condition} {date_condition}
        ORDER BY p.Date DESC, p.PreviewCode ASC
        """

        cursor.execute(query, branch_params + date_params)
        results = cursor.fetchall()
        conn.close()

        if results:
            branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
            branch_name = branch_names.get(branch_id, 'غير محدد')
            days_text = f"{days} ايام" if days else "للكل"

            total_previews = len(results)

            # تحديد عدد المعاينات المعروضة (أقصى 100)
            max_display = 100
            display_count = min(total_previews, max_display)

            report = f"👁️ **المعاينات بدون اجتماعات في {branch_name} خلال {days_text}**\n\n"

            # عرض أول 100 معاينة فقط
            for i, (preview_code, name, phone, branch, preview_date) in enumerate(results[:max_display], 1):
                date_str = preview_date.strftime('%Y-%m-%d') if preview_date else 'غير محدد'
                report += f"{i:03d} - {name or 'غير محدد'}\n"
                report += f"     📞 {phone or 'غير محدد'} | 🏢 {branch or 'غير محدد'}\n"
                report += f"     👁️ معاينة: {date_str}\n\n"

            if total_previews > max_display:
                report += f"⚠️ **تم عرض أول {max_display} معاينة من أصل {total_previews:,} معاينة**\n\n"

            report += f"📈 **إجمالي المعاينات بدون اجتماعات:** {total_previews:,} معاينة\n"
            report += f"🔢 **العدد المعروض:** {display_count} من {total_previews}"

            return report
        else:
            return "❌ لا توجد بيانات للفترة المحددة"

    except Exception as e:
        return f"❌ حدث خطأ أثناء تحضير التقرير:\n{str(e)}"

def get_customers_with_meetings_no_designs(branch_id, days):
    """الحصول على العملاء الذين لهم اجتماعات بدون تصميمات"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات"

        cursor = conn.cursor()

        # تحديد شرط الفرع
        if branch_id == '3':  # للكل
            branch_condition = ""
            branch_params = []
        else:
            actual_branch_id = '3' if branch_id == '1' else '2'  # التجمع=3, مدينة نصر=2
            branch_condition = "AND m.BranchId = ?"
            branch_params = [actual_branch_id]

        # تحديد شرط التاريخ
        if days is None:  # للكل
            date_condition = ""
            date_params = []
        else:
            date_condition = "AND m.Date >= DATEADD(day, -?, GETDATE())"
            date_params = [days]

        query = f"""
        SELECT
            m.MeetingCode,
            c.NameAr,
            c.MainPhoneNo,
            b.NameAr as BranchName,
            m.Date as MeetingDate,
            p.Date as PreviewDate
        FROM Sys_Meetings m
        INNER JOIN Acc_Customers c ON m.CustomerId = c.CustomerCode
        LEFT JOIN Sys_Branches b ON m.BranchId = b.BranchId
        LEFT JOIN Sys_Previews p ON m.PreviewId = p.PreviewId
        WHERE m.IsDeleted = 0 AND c.Status = 1 AND c.IsDeleted = 0
        AND m.DesignId IS NULL {branch_condition} {date_condition}
        ORDER BY m.Date DESC, m.MeetingCode ASC
        """

        cursor.execute(query, branch_params + date_params)
        results = cursor.fetchall()
        conn.close()

        if results:
            branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
            branch_name = branch_names.get(branch_id, 'غير محدد')
            days_text = f"{days} ايام" if days else "للكل"

            total_meetings = len(results)

            # تحديد عدد الاجتماعات المعروضة (أقصى 100)
            max_display = 100
            display_count = min(total_meetings, max_display)

            report = f"🤝 **الاجتماعات بدون تصميمات في {branch_name} خلال {days_text}**\n\n"

            # عرض أول 100 اجتماع فقط
            for i, (meeting_code, name, phone, branch, meeting_date, preview_date) in enumerate(results[:max_display], 1):
                meeting_str = meeting_date.strftime('%Y-%m-%d') if meeting_date else 'غير محدد'
                preview_str = preview_date.strftime('%Y-%m-%d') if preview_date else 'غير محدد'
                report += f"{i:03d} - {name or 'غير محدد'}\n"
                report += f"     📞 {phone or 'غير محدد'} | 🏢 {branch or 'غير محدد'}\n"
                report += f"     👁️ معاينة: {preview_str} | 🤝 اجتماع: {meeting_str}\n\n"

            if total_meetings > max_display:
                report += f"⚠️ **تم عرض أول {max_display} اجتماع من أصل {total_meetings:,} اجتماع**\n\n"

            report += f"📈 **إجمالي الاجتماعات بدون تصميمات:** {total_meetings:,} اجتماع\n"
            report += f"🔢 **العدد المعروض:** {display_count} من {total_meetings}"

            return report
        else:
            return "❌ لا توجد بيانات للفترة المحددة"

    except Exception as e:
        return f"❌ حدث خطأ أثناء تحضير التقرير:\n{str(e)}"

def get_customers_with_designs_no_contracts(branch_id, days):
    """الحصول على العملاء الذين لهم تصميمات بدون عقود"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات"

        cursor = conn.cursor()

        # تحديد شرط الفرع
        if branch_id == '3':  # للكل
            branch_condition = ""
            branch_params = []
        else:
            actual_branch_id = '3' if branch_id == '1' else '2'  # التجمع=3, مدينة نصر=2
            branch_condition = "AND d.BranchId = ?"
            branch_params = [actual_branch_id]

        # تحديد شرط التاريخ
        if days is None:  # للكل
            date_condition = ""
            date_params = []
        else:
            date_condition = "AND d.Date >= DATEADD(day, -?, GETDATE())"
            date_params = [days]

        query = f"""
        SELECT
            d.DesignCode,
            c.NameAr,
            c.MainPhoneNo,
            b.NameAr as BranchName,
            d.Date as DesignDate,
            m.Date as MeetingDate,
            p.Date as PreviewDate
        FROM Sys_Designs d
        INNER JOIN Acc_Customers c ON d.CustomerId = c.CustomerCode
        LEFT JOIN Sys_Branches b ON d.BranchId = b.BranchId
        LEFT JOIN Sys_Meetings m ON d.MeetingId = m.MeetingId
        LEFT JOIN Sys_Previews p ON m.PreviewId = p.PreviewId
        WHERE d.IsDeleted = 0 AND c.Status = 1 AND c.IsDeleted = 0
        AND d.ContractId IS NULL {branch_condition} {date_condition}
        ORDER BY d.Date DESC, d.DesignCode ASC
        """

        cursor.execute(query, branch_params + date_params)
        results = cursor.fetchall()
        conn.close()

        if results:
            branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
            branch_name = branch_names.get(branch_id, 'غير محدد')
            days_text = f"{days} ايام" if days else "للكل"

            total_designs = len(results)

            # تحديد عدد التصميمات المعروضة (أقصى 100)
            max_display = 100
            display_count = min(total_designs, max_display)

            report = f"🎨 **التصميمات بدون عقود في {branch_name} خلال {days_text}**\n\n"

            # عرض أول 100 تصميم فقط
            for i, (design_code, name, phone, branch, design_date, meeting_date, preview_date) in enumerate(results[:max_display], 1):
                design_str = design_date.strftime('%Y-%m-%d') if design_date else 'غير محدد'
                meeting_str = meeting_date.strftime('%Y-%m-%d') if meeting_date else 'غير محدد'
                preview_str = preview_date.strftime('%Y-%m-%d') if preview_date else 'غير محدد'
                report += f"{i:03d} - {name or 'غير محدد'}\n"
                report += f"     📞 {phone or 'غير محدد'} | 🏢 {branch or 'غير محدد'}\n"
                report += f"     👁️ معاينة: {preview_str}\n"
                report += f"     🤝 اجتماع: {meeting_str} | 🎨 تصميم: {design_str}\n\n"

            if total_designs > max_display:
                report += f"⚠️ **تم عرض أول {max_display} تصميم من أصل {total_designs:,} تصميم**\n\n"

            report += f"📈 **إجمالي التصميمات بدون عقود:** {total_designs:,} تصميم\n"
            report += f"🔢 **العدد المعروض:** {display_count} من {total_designs}"

            return report
        else:
            return "❌ لا توجد بيانات للفترة المحددة"

    except Exception as e:
        return f"❌ حدث خطأ أثناء تحضير التقرير:\n{str(e)}"

def get_previews_count_by_regions(branch_id, days):
    """الحصول على أعداد المعاينات حسب المناطق"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات"

        cursor = conn.cursor()

        # تحديد شرط الفرع
        if branch_id == '3':  # للكل
            branch_condition = ""
            branch_params = []
        else:
            actual_branch_id = '3' if branch_id == '1' else '2'  # التجمع=3, مدينة نصر=2
            branch_condition = "AND p.BranchId = ?"
            branch_params = [actual_branch_id]

        # تحديد شرط التاريخ
        if days is None:  # للكل
            date_condition = ""
            date_params = []
        else:
            date_condition = "AND p.Date >= DATEADD(day, -?, GETDATE())"
            date_params = [days]

        query = f"""
        SELECT
            city.NameAr as CityName,
            COUNT(DISTINCT p.PreviewId) as PreviewsCount,
            COUNT(DISTINCT c.CustomerCode) as CustomersCount
        FROM Sys_Previews p
        INNER JOIN Acc_Customers c ON p.CustomerId = c.CustomerCode
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        WHERE p.IsDeleted = 0 AND c.Status = 1 AND c.IsDeleted = 0 {branch_condition} {date_condition}
        GROUP BY city.NameAr, city.CityId
        ORDER BY PreviewsCount DESC
        """

        cursor.execute(query, branch_params + date_params)
        results = cursor.fetchall()
        conn.close()

        if results:
            branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
            branch_name = branch_names.get(branch_id, 'غير محدد')
            days_text = f"{days} ايام" if days else "للكل"

            total_previews = sum(row[1] for row in results)
            total_customers = sum(row[2] for row in results)

            report = f"👁️ **أعداد المعاينات حسب المناطق في {branch_name} خلال {days_text}**\n\n"

            for i, (city_name, previews_count, customers_count) in enumerate(results, 1):
                city_display = city_name or 'غير محدد'
                report += f"{i:02d}. 🌍 **{city_display}**\n"
                report += f"     👁️ معاينات: {previews_count:,}\n"
                report += f"     👥 عملاء: {customers_count:,}\n\n"

            report += f"📈 **إجمالي المعاينات:** {total_previews:,} معاينة\n"
            report += f"👥 **إجمالي العملاء:** {total_customers:,} عميل\n"
            report += f"📊 **عدد المناطق:** {len(results)} منطقة\n"
            report += f"🔢 **العدد:** {total_previews}"

            return report
        else:
            return "❌ لا توجد بيانات للفترة المحددة"

    except Exception as e:
        return f"❌ حدث خطأ أثناء تحضير التقرير:\n{str(e)}"

def get_previews_count_by_social_media(branch_id, days):
    """الحصول على أعداد المعاينات حسب وسائل التواصل"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات"

        cursor = conn.cursor()

        # تحديد شرط الفرع
        if branch_id == '3':  # للكل
            branch_condition = ""
            branch_params = []
        else:
            actual_branch_id = '3' if branch_id == '1' else '2'  # التجمع=3, مدينة نصر=2
            branch_condition = "AND p.BranchId = ?"
            branch_params = [actual_branch_id]

        # تحديد شرط التاريخ
        if days is None:  # للكل
            date_condition = ""
            date_params = []
        else:
            date_condition = "AND p.Date >= DATEADD(day, -?, GETDATE())"
            date_params = [days]

        query = f"""
        SELECT
            sm.NameAr as SocialMediaName,
            COUNT(DISTINCT p.PreviewId) as PreviewsCount,
            COUNT(DISTINCT c.CustomerCode) as CustomersCount
        FROM Sys_Previews p
        INNER JOIN Acc_Customers c ON p.CustomerId = c.CustomerCode
        LEFT JOIN Sys_SocialMedia sm ON c.SocialMediaId = sm.SocialMediaId
        WHERE p.IsDeleted = 0 AND c.Status = 1 AND c.IsDeleted = 0 {branch_condition} {date_condition}
        GROUP BY sm.NameAr, sm.SocialMediaId
        ORDER BY PreviewsCount DESC
        """

        cursor.execute(query, branch_params + date_params)
        results = cursor.fetchall()
        conn.close()

        if results:
            branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
            branch_name = branch_names.get(branch_id, 'غير محدد')
            days_text = f"{days} ايام" if days else "للكل"

            total_previews = sum(row[1] for row in results)
            total_customers = sum(row[2] for row in results)

            report = f"📱 **أعداد المعاينات حسب وسائل التواصل في {branch_name} خلال {days_text}**\n\n"

            for i, (social_media_name, previews_count, customers_count) in enumerate(results, 1):
                social_display = social_media_name or 'غير محدد'
                report += f"{i:02d}. 📱 **{social_display}**\n"
                report += f"     👁️ معاينات: {previews_count:,}\n"
                report += f"     👥 عملاء: {customers_count:,}\n\n"

            report += f"📈 **إجمالي المعاينات:** {total_previews:,} معاينة\n"
            report += f"👥 **إجمالي العملاء:** {total_customers:,} عميل\n"
            report += f"📊 **عدد وسائل التواصل:** {len(results)} وسيلة\n"
            report += f"🔢 **العدد:** {total_previews}"

            return report
        else:
            return "❌ لا توجد بيانات للفترة المحددة"

    except Exception as e:
        return f"❌ حدث خطأ أثناء تحضير التقرير:\n{str(e)}"

def get_customers_with_multiple_previews():
    """الحصول على العملاء الذين لديهم أكثر من معاينة"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات"

        cursor = conn.cursor()

        query = """
        SELECT
            c.CustomerCode,
            c.NameAr,
            c.MainPhoneNo,
            b.NameAr as BranchName,
            COUNT(p.PreviewId) as PreviewsCount,
            COUNT(CASE WHEN p.IsDeleted = 0 THEN 1 END) as ActivePreviews,
            COUNT(CASE WHEN p.IsDeleted = 1 THEN 1 END) as DeletedPreviews
        FROM Acc_Customers c
        INNER JOIN Sys_Previews p ON c.CustomerCode = p.CustomerId
        LEFT JOIN Sys_Branches b ON c.BranchId = b.BranchId
        WHERE c.IsDeleted = 0
        GROUP BY c.CustomerCode, c.NameAr, c.MainPhoneNo, b.NameAr
        HAVING COUNT(p.PreviewId) > 1
        ORDER BY PreviewsCount DESC, c.CustomerCode ASC
        """

        cursor.execute(query)
        results = cursor.fetchall()
        conn.close()

        if results:
            total_customers = len(results)
            total_previews = sum(row[4] for row in results)

            report = f"👁️ **العملاء الذين لديهم أكثر من معاينة**\n\n"
            report += f"👥 **إجمالي العملاء:** {total_customers:,} عميل\n"
            report += f"📈 **إجمالي المعاينات:** {total_previews:,} معاينة\n"
            report += f"📊 **متوسط المعاينات لكل عميل:** {total_previews/total_customers:.1f} معاينة\n\n"

            for i, (customer_code, name, phone, branch, total_previews_count, active_previews, deleted_previews) in enumerate(results, 1):
                report += f"{i:03d}. `{customer_code}` - {name or 'غير محدد'}\n"
                report += f"     📞 {phone or 'غير محدد'} | 🏢 {branch or 'غير محدد'}\n"
                report += f"     👁️ إجمالي: {total_previews_count} | ✅ نشطة: {active_previews} | 🗑️ محذوفة: {deleted_previews}\n\n"

            report += f"🔢 **العدد الإجمالي:** {total_customers} عميل"
            return report
        else:
            return "❌ لا يوجد عملاء لديهم أكثر من معاينة"

    except Exception as e:
        return f"❌ حدث خطأ أثناء تحضير التقرير:\n{str(e)}"

def get_customers_without_previews_count(branch_id, days):
    """الحصول على عدد العملاء الذين لا يوجد لهم معاينات"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات"

        cursor = conn.cursor()

        # تحديد شرط الفرع
        if branch_id == '3':  # للكل
            branch_condition = ""
            branch_params = []
        else:
            actual_branch_id = '3' if branch_id == '1' else '2'  # التجمع=3, مدينة نصر=2
            branch_condition = "AND c.BranchId = ?"
            branch_params = [actual_branch_id]

        # تحديد شرط التاريخ
        if days is None:  # للكل
            date_condition = ""
            date_params = []
        else:
            date_condition = "AND c.AddDate >= DATEADD(day, -?, GETDATE())"
            date_params = [days]

        # حساب العملاء النشطين
        query_active = f"""
        SELECT COUNT(*) FROM Acc_Customers c
        WHERE c.Status = 1 AND c.IsDeleted = 0 {branch_condition} {date_condition}
        """
        cursor.execute(query_active, branch_params + date_params)
        result = cursor.fetchone()
        active_customers = result[0] if result else 0

        # حساب العملاء اللي عملتلهم معاينة (كل الحالات - نشطة ومحذوفة)
        # نحسب من جدول المعاينات كل العملاء اللي عملتلهم معاينة
        query_with_previews = f"""
        SELECT COUNT(DISTINCT p.CustomerId)
        FROM Sys_Previews p
        """
        cursor.execute(query_with_previews)
        result = cursor.fetchone()
        all_customers_with_previews = result[0] if result else 0

        # نحسب العملاء النشطين اللي عملتلهم معاينة (كل الحالات - نشطة ومحذوفة)
        # نستخدم نفس المنطق اللي في /test
        query_active_with_previews = f"""
        SELECT COUNT(DISTINCT p.CustomerId)
        FROM Sys_Previews p
        INNER JOIN Acc_Customers c ON p.CustomerId = c.CustomerCode
        WHERE c.Status = 1 AND c.IsDeleted = 0 {branch_condition}
        """
        cursor.execute(query_active_with_previews, branch_params)
        result = cursor.fetchone()
        customers_with_previews = result[0] if result else 0

        # العملاء المحتاجين معاينة = العملاء النشطين - كل اللي عملتلهم معاينة (254)
        customers_need_previews = active_customers - all_customers_with_previews

        conn.close()

        branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
        branch_name = branch_names.get(branch_id, 'غير محدد')
        days_text = f"{days} ايام" if days else "للكل"

        report = f"📊 **إحصائيات العملاء المحتاجين معاينة**\n\n"
        report += f"🏢 **الفرع:** {branch_name}\n"
        report += f"📅 **الفترة:** {days_text}\n\n"
        report += f"👥 **العملاء النشطين:** {active_customers:,} عميل\n"
        report += f"✅ **عملتلهم معاينة خلاص:** {all_customers_with_previews:,} عميل\n"
        report += f"🎯 **محتاجين معاينة:** {customers_need_previews:,} عميل\n\n"
        report += f"🔢 **العدد المطلوب:** {customers_need_previews}"

        return report, customers_need_previews

    except Exception as e:
        return f"❌ حدث خطأ أثناء تحضير التقرير:\n{str(e)}", 0

def get_customers_with_previews_no_meetings_count(branch_id, days):
    """الحصول على عدد العملاء الذين لهم معاينات بدون اجتماعات"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات"

        cursor = conn.cursor()

        # تحديد شرط الفرع
        if branch_id == '3':  # للكل
            branch_condition = ""
            branch_params = []
        else:
            actual_branch_id = '3' if branch_id == '1' else '2'  # التجمع=3, مدينة نصر=2
            branch_condition = "AND p.BranchId = ?"
            branch_params = [actual_branch_id]

        # تحديد شرط التاريخ
        if days is None:  # للكل
            date_condition = ""
            date_params = []
        else:
            date_condition = "AND p.Date >= DATEADD(day, -?, GETDATE())"
            date_params = [days]

        query = f"""
        SELECT COUNT(DISTINCT c.CustomerCode) as CustomersCount
        FROM Acc_Customers c
        INNER JOIN Sys_Previews p ON c.CustomerCode = p.CustomerId
        WHERE c.Status = 1 AND c.IsDeleted = 0 AND p.IsDeleted = 0
        AND p.MeetingId IS NULL {branch_condition} {date_condition}
        """

        cursor.execute(query, branch_params + date_params)
        result = cursor.fetchone()
        conn.close()

        if result:
            branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
            branch_name = branch_names.get(branch_id, 'غير محدد')
            days_text = f"{days} ايام" if days else "للكل"

            customers_count = result[0]

            report = f"📊 **إحصائيات العملاء لهم معاينات بدون اجتماعات**\n\n"
            report += f"🏢 **الفرع:** {branch_name}\n"
            report += f"📅 **الفترة:** {days_text}\n\n"
            report += f"👥 **عدد العملاء لهم معاينات بدون اجتماعات:** {customers_count:,} عميل\n\n"
            report += f"🔢 **العدد الإجمالي:** {customers_count}"

            return report, customers_count
        else:
            return "❌ لا توجد بيانات للفترة المحددة", 0

    except Exception as e:
        return f"❌ حدث خطأ أثناء تحضير التقرير:\n{str(e)}", 0

def get_customers_with_meetings_no_designs_count(branch_id, days):
    """الحصول على عدد العملاء الذين لهم اجتماعات بدون تصميمات"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات"

        cursor = conn.cursor()

        # تحديد شرط الفرع
        if branch_id == '3':  # للكل
            branch_condition = ""
            branch_params = []
        else:
            actual_branch_id = '3' if branch_id == '1' else '2'  # التجمع=3, مدينة نصر=2
            branch_condition = "AND m.BranchId = ?"
            branch_params = [actual_branch_id]

        # تحديد شرط التاريخ
        if days is None:  # للكل
            date_condition = ""
            date_params = []
        else:
            date_condition = "AND m.Date >= DATEADD(day, -?, GETDATE())"
            date_params = [days]

        query = f"""
        SELECT COUNT(DISTINCT c.CustomerCode) as CustomersCount
        FROM Acc_Customers c
        INNER JOIN Sys_Meetings m ON c.CustomerCode = m.CustomerId
        WHERE c.Status = 1 AND c.IsDeleted = 0 AND m.IsDeleted = 0
        AND m.DesignId IS NULL {branch_condition} {date_condition}
        """

        cursor.execute(query, branch_params + date_params)
        result = cursor.fetchone()
        conn.close()

        if result:
            branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
            branch_name = branch_names.get(branch_id, 'غير محدد')
            days_text = f"{days} ايام" if days else "للكل"

            customers_count = result[0]

            report = f"📊 **إحصائيات العملاء لهم اجتماعات بدون تصميمات**\n\n"
            report += f"🏢 **الفرع:** {branch_name}\n"
            report += f"📅 **الفترة:** {days_text}\n\n"
            report += f"👥 **عدد العملاء لهم اجتماعات بدون تصميمات:** {customers_count:,} عميل\n\n"
            report += f"🔢 **العدد الإجمالي:** {customers_count}"

            return report, customers_count
        else:
            return "❌ لا توجد بيانات للفترة المحددة", 0

    except Exception as e:
        return f"❌ حدث خطأ أثناء تحضير التقرير:\n{str(e)}", 0

def get_customers_with_designs_no_contracts_count(branch_id, days):
    """الحصول على عدد العملاء الذين لهم تصميمات بدون عقود"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات"

        cursor = conn.cursor()

        # تحديد شرط الفرع
        if branch_id == '3':  # للكل
            branch_condition = ""
            branch_params = []
        else:
            actual_branch_id = '3' if branch_id == '1' else '2'  # التجمع=3, مدينة نصر=2
            branch_condition = "AND d.BranchId = ?"
            branch_params = [actual_branch_id]

        # تحديد شرط التاريخ
        if days is None:  # للكل
            date_condition = ""
            date_params = []
        else:
            date_condition = "AND d.Date >= DATEADD(day, -?, GETDATE())"
            date_params = [days]

        query = f"""
        SELECT COUNT(DISTINCT c.CustomerCode) as CustomersCount
        FROM Acc_Customers c
        INNER JOIN Sys_Designs d ON c.CustomerCode = d.CustomerId
        WHERE c.Status = 1 AND c.IsDeleted = 0 AND d.IsDeleted = 0
        AND d.ContractId IS NULL {branch_condition} {date_condition}
        """

        cursor.execute(query, branch_params + date_params)
        result = cursor.fetchone()
        conn.close()

        if result:
            branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
            branch_name = branch_names.get(branch_id, 'غير محدد')
            days_text = f"{days} ايام" if days else "للكل"

            customers_count = result[0]

            report = f"📊 **إحصائيات العملاء لهم تصميمات بدون عقود**\n\n"
            report += f"🏢 **الفرع:** {branch_name}\n"
            report += f"📅 **الفترة:** {days_text}\n\n"
            report += f"👥 **عدد العملاء لهم تصميمات بدون عقود:** {customers_count:,} عميل\n\n"
            report += f"🔢 **العدد الإجمالي:** {customers_count}"

            return report, customers_count
        else:
            return "❌ لا توجد بيانات للفترة المحددة", 0

    except Exception as e:
        return f"❌ حدث خطأ أثناء تحضير التقرير:\n{str(e)}", 0

def get_customers_without_previews(branch_id, days):
    """الحصول على العملاء الذين لا يوجد لهم معاينات"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات"

        cursor = conn.cursor()

        # تحديد شرط الفرع
        if branch_id == '3':  # للكل
            branch_condition = ""
            branch_params = []
        else:
            actual_branch_id = '3' if branch_id == '1' else '2'  # التجمع=3, مدينة نصر=2
            branch_condition = "AND c.BranchId = ?"
            branch_params = [actual_branch_id]

        # تحديد شرط التاريخ
        if days is None:  # للكل
            date_condition = ""
            date_params = []
        else:
            date_condition = "AND c.AddDate >= DATEADD(day, -?, GETDATE())"
            date_params = [days]

        query = f"""
        SELECT
            c.CustomerCode,
            c.NameAr,
            c.MainPhoneNo,
            b.NameAr as BranchName,
            c.AddDate
        FROM Acc_Customers c
        LEFT JOIN Sys_Branches b ON c.BranchId = b.BranchId
        LEFT JOIN Sys_Previews p ON c.CustomerCode = p.CustomerId
        WHERE c.Status = 1 AND c.IsDeleted = 0
        AND p.CustomerId IS NULL {branch_condition} {date_condition}
        ORDER BY c.AddDate DESC, c.CustomerCode ASC
        """

        cursor.execute(query, branch_params + date_params)
        results = cursor.fetchall()
        conn.close()

        if results:
            branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
            branch_name = branch_names.get(branch_id, 'غير محدد')
            days_text = f"{days} ايام" if days else "للكل"

            total_customers = len(results)

            # تحديد عدد العملاء المعروضين (أقصى 100 عميل)
            max_display = 100
            display_count = min(total_customers, max_display)

            report = f"📋 **العملاء بدون معاينات في {branch_name} خلال {days_text}**\n\n"

            # عرض أول 100 عميل فقط
            for i, (customer_code, name, phone, branch, add_date) in enumerate(results[:max_display], 1):
                date_str = add_date.strftime('%Y-%m-%d') if add_date else 'غير محدد'
                report += f"{i:03d} - {name or 'غير محدد'}\n"
                report += f"     📞 {phone or 'غير محدد'} | 🏢 {branch or 'غير محدد'}\n"
                report += f"     📅 {date_str}\n\n"

            if total_customers > max_display:
                report += f"⚠️ **تم عرض أول {max_display} عميل من أصل {total_customers:,} عميل**\n\n"

            report += f"📈 **إجمالي العملاء بدون معاينات:** {total_customers:,} عميل\n"
            report += f"🔢 **العدد المعروض:** {display_count} من {total_customers}"

            return report
        else:
            return "❌ لا توجد بيانات للفترة المحددة"

    except Exception as e:
        return f"❌ حدث خطأ أثناء تحضير التقرير:\n{str(e)}"

def get_customers_without_designs(branch_id, days):
    """الحصول على العملاء الذين تم عمل معاينات لهم ولكن لا يوجد تصميمات"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات"

        cursor = conn.cursor()

        # تحديد شرط الفرع
        if branch_id == '3':  # للكل
            branch_condition = ""
            branch_params = []
        else:
            actual_branch_id = '3' if branch_id == '1' else '2'  # التجمع=3, مدينة نصر=2
            branch_condition = "AND c.BranchId = ?"
            branch_params = [actual_branch_id]

        # تحديد شرط التاريخ
        if days is None:  # للكل
            date_condition = ""
            date_params = []
        else:
            date_condition = "AND c.AddDate >= DATEADD(day, -?, GETDATE())"
            date_params = [days]

        query = f"""
        SELECT DISTINCT
            c.CustomerCode,
            c.NameAr,
            c.MainPhoneNo,
            b.NameAr as BranchName,
            c.AddDate,
            p.Date as PreviewDate
        FROM Acc_Customers c
        LEFT JOIN Sys_Branches b ON c.BranchId = b.BranchId
        INNER JOIN Sys_Previews p ON c.CustomerCode = p.CustomerId
        LEFT JOIN Sys_Designs d ON c.CustomerCode = d.CustomerId
        WHERE c.Status=1 AND c.IsDeleted=0
        AND d.CustomerId IS NULL {branch_condition} {date_condition}
        ORDER BY c.AddDate DESC, c.CustomerCode ASC
        """

        cursor.execute(query, branch_params + date_params)
        results = cursor.fetchall()
        conn.close()

        if results:
            branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
            branch_name = branch_names.get(branch_id, 'غير محدد')
            days_text = f"{days} ايام" if days else "للكل"

            total_customers = len(results)

            report = f"🎨 **العملاء بدون تصميمات في {branch_name} خلال {days_text}**\n\n"

            for i, (customer_code, name, phone, branch, add_date, preview_date) in enumerate(results, 1):
                date_str = add_date.strftime('%Y-%m-%d') if add_date else 'غير محدد'
                preview_str = preview_date.strftime('%Y-%m-%d') if preview_date else 'غير محدد'
                report += f"{i:03d}. `{customer_code}` - {name or 'غير محدد'}\n"
                report += f"     📞 {phone or 'غير محدد'} | 🏢 {branch or 'غير محدد'}\n"
                report += f"     📅 إضافة: {date_str} | 👁️ معاينة: {preview_str}\n\n"

            report += f"📈 **إجمالي العملاء بدون تصميمات:** {total_customers:,} عميل\n"
            report += f"🔢 **العدد:** {total_customers}"

            return report
        else:
            return "❌ لا توجد بيانات للفترة المحددة"

    except Exception as e:
        return f"❌ حدث خطأ أثناء تحضير التقرير:\n{str(e)}"

def get_customers_without_contracts(branch_id, days):
    """الحصول على العملاء الذين تم عمل تصميمات لهم ولكن لا يوجد عقود"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات"

        cursor = conn.cursor()

        # تحديد شرط الفرع
        if branch_id == '3':  # للكل
            branch_condition = ""
            branch_params = []
        else:
            actual_branch_id = '3' if branch_id == '1' else '2'  # التجمع=3, مدينة نصر=2
            branch_condition = "AND c.BranchId = ?"
            branch_params = [actual_branch_id]

        # تحديد شرط التاريخ
        if days is None:  # للكل
            date_condition = ""
            date_params = []
        else:
            date_condition = "AND c.AddDate >= DATEADD(day, -?, GETDATE())"
            date_params = [days]

        query = f"""
        SELECT DISTINCT
            c.CustomerCode,
            c.NameAr,
            c.MainPhoneNo,
            b.NameAr as BranchName,
            c.AddDate,
            p.Date as PreviewDate,
            d.Date as DesignDate
        FROM Acc_Customers c
        LEFT JOIN Sys_Branches b ON c.BranchId = b.BranchId
        INNER JOIN Sys_Previews p ON c.CustomerCode = p.CustomerId
        INNER JOIN Sys_Designs d ON c.CustomerCode = d.CustomerId
        LEFT JOIN Acc_Contracts ct ON c.CustomerCode = ct.CustomerId
        WHERE c.Status=1 AND c.IsDeleted=0
        AND ct.CustomerId IS NULL {branch_condition} {date_condition}
        ORDER BY c.AddDate DESC, c.CustomerCode ASC
        """

        cursor.execute(query, branch_params + date_params)
        results = cursor.fetchall()
        conn.close()

        if results:
            branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
            branch_name = branch_names.get(branch_id, 'غير محدد')
            days_text = f"{days} ايام" if days else "للكل"

            total_customers = len(results)

            report = f"📄 **العملاء بدون عقود في {branch_name} خلال {days_text}**\n\n"

            for i, (customer_code, name, phone, branch, add_date, preview_date, design_date) in enumerate(results, 1):
                date_str = add_date.strftime('%Y-%m-%d') if add_date else 'غير محدد'
                preview_str = preview_date.strftime('%Y-%m-%d') if preview_date else 'غير محدد'
                design_str = design_date.strftime('%Y-%m-%d') if design_date else 'غير محدد'
                report += f"{i:03d}. `{customer_code}` - {name or 'غير محدد'}\n"
                report += f"     📞 {phone or 'غير محدد'} | 🏢 {branch or 'غير محدد'}\n"
                report += f"     📅 إضافة: {date_str}\n"
                report += f"     👁️ معاينة: {preview_str} | 🎨 تصميم: {design_str}\n\n"

            report += f"📈 **إجمالي العملاء بدون عقود:** {total_customers:,} عميل\n"
            report += f"🔢 **العدد:** {total_customers}"

            return report
        else:
            return "❌ لا توجد بيانات للفترة المحددة"

    except Exception as e:
        return f"❌ حدث خطأ أثناء تحضير التقرير:\n{str(e)}"

def get_customers_by_social_media(branch_id, days):
    """الحصول على عدد العملاء حسب وسائل التواصل"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات"

        cursor = conn.cursor()

        # تحديد شرط الفرع
        if branch_id == '3':  # للكل
            branch_condition = ""
            branch_params = []
        else:
            actual_branch_id = '3' if branch_id == '1' else '2'  # التجمع=3, مدينة نصر=2
            branch_condition = "AND c.BranchId = ?"
            branch_params = [actual_branch_id]

        # تحديد شرط التاريخ
        if days is None:  # للكل
            date_condition = ""
            date_params = []
        else:
            date_condition = "AND c.AddDate >= DATEADD(day, -?, GETDATE())"
            date_params = [days]

        query = f"""
        SELECT
            ISNULL(sm.NameAr, 'غير محدد') as SocialMediaName,
            COUNT(*) as CustomerCount
        FROM Acc_Customers c
        LEFT JOIN Sys_SocialMedia sm ON c.SocialMediaId = sm.SocialMediaId
        LEFT JOIN Sys_Branches b ON c.BranchId = b.BranchId
        WHERE c.Status=1 AND c.IsDeleted=0 {branch_condition} {date_condition}
        GROUP BY sm.NameAr
        ORDER BY CustomerCount DESC
        """

        cursor.execute(query, branch_params + date_params)
        results = cursor.fetchall()
        conn.close()

        if results:
            branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
            branch_name = branch_names.get(branch_id, 'غير محدد')
            days_text = f"{days} ايام" if days else "للكل"

            total_customers = sum(row[1] for row in results)

            report = f"📱 **عدد العملاء حسب وسائل التواصل في {branch_name} خلال {days_text}**\n\n"

            for i, (social_media_name, count) in enumerate(results, 1):
                percentage = (count / total_customers * 100) if total_customers > 0 else 0

                # اختيار الرمز المناسب لكل وسيلة تواصل
                if 'فيس' in social_media_name.lower() or 'facebook' in social_media_name.lower():
                    icon = "📘"
                elif 'واتس' in social_media_name.lower() or 'whatsapp' in social_media_name.lower():
                    icon = "💬"
                elif 'انستا' in social_media_name.lower() or 'instagram' in social_media_name.lower():
                    icon = "📷"
                elif 'تويتر' in social_media_name.lower() or 'twitter' in social_media_name.lower():
                    icon = "🐦"
                elif 'يوتيوب' in social_media_name.lower() or 'youtube' in social_media_name.lower():
                    icon = "📺"
                elif 'تيك توك' in social_media_name.lower() or 'tiktok' in social_media_name.lower():
                    icon = "🎵"
                elif 'لينكد' in social_media_name.lower() or 'linkedin' in social_media_name.lower():
                    icon = "💼"
                else:
                    icon = "📱"

                report += f"{i:02d}. {icon} **{social_media_name}**\n"
                report += f"     👥 {count:,} عميل ({percentage:.1f}%)\n\n"

            report += f"📈 **إجمالي العملاء:** {total_customers:,} عميل\n"
            report += f"📊 **عدد وسائل التواصل:** {len(results)} وسيلة\n"
            report += f"🔢 **العدد:** {total_customers}"

            return report
        else:
            return "❌ لا توجد بيانات للفترة المحددة"

    except Exception as e:
        return f"❌ حدث خطأ أثناء تحضير التقرير:\n{str(e)}"

def get_customers_by_regions(branch_id, days):
    """الحصول على عدد العملاء حسب المناطق"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات"

        cursor = conn.cursor()

        # تحديد شرط الفرع
        if branch_id == '3':  # للكل
            branch_condition = ""
            branch_params = []
        else:
            actual_branch_id = '3' if branch_id == '1' else '2'  # التجمع=3, مدينة نصر=2
            branch_condition = "AND c.BranchId = ?"
            branch_params = [actual_branch_id]

        # تحديد شرط التاريخ
        if days is None:  # للكل
            date_condition = ""
            date_params = []
        else:
            date_condition = "AND c.AddDate >= DATEADD(day, -?, GETDATE())"
            date_params = [days]

        query = f"""
        SELECT
            ISNULL(city.NameAr, 'غير محدد') as CityName,
            COUNT(*) as CustomerCount
        FROM Acc_Customers c
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Branches b ON c.BranchId = b.BranchId
        WHERE c.Status=1 AND c.IsDeleted=0 {branch_condition} {date_condition}
        GROUP BY city.NameAr
        ORDER BY CustomerCount DESC
        """

        cursor.execute(query, branch_params + date_params)
        results = cursor.fetchall()
        conn.close()

        if results:
            branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
            branch_name = branch_names.get(branch_id, 'غير محدد')
            days_text = f"{days} ايام" if days else "للكل"

            total_customers = sum(row[1] for row in results)

            report = f"🌍 **عدد العملاء حسب المناطق في {branch_name} خلال {days_text}**\n\n"

            for i, (city_name, count) in enumerate(results, 1):
                percentage = (count / total_customers * 100) if total_customers > 0 else 0
                report += f"{i:02d}. 📍 **{city_name}**\n"
                report += f"     👥 {count:,} عميل ({percentage:.1f}%)\n\n"

            report += f"📈 **إجمالي العملاء:** {total_customers:,} عميل\n"
            report += f"📊 **عدد المناطق:** {len(results)} منطقة\n"
            report += f"🔢 **العدد:** {total_customers}"

            return report
        else:
            return "❌ لا توجد بيانات للفترة المحددة"

    except Exception as e:
        return f"❌ حدث خطأ أثناء تحضير التقرير:\n{str(e)}"

def get_customers_list(branch_id, days):
    """الحصول على قائمة العملاء"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات"
        
        cursor = conn.cursor()
        
        # تحديد شرط الفرع
        if branch_id == '3':  # للكل
            branch_condition = ""
            branch_params = []
        else:
            actual_branch_id = '3' if branch_id == '1' else '2'  # التجمع=3, مدينة نصر=2
            branch_condition = "AND c.BranchId = ?"
            branch_params = [actual_branch_id]
        
        # تحديد شرط التاريخ
        if days is None:  # للكل
            date_condition = ""
            date_params = []
        else:
            date_condition = "AND c.AddDate >= DATEADD(day, -?, GETDATE())"
            date_params = [days]
        
        query = f"""
        SELECT
            c.CustomerCode,
            c.NameAr,
            c.MainPhoneNo,
            b.NameAr as BranchName,
            c.AddDate
        FROM Acc_Customers c
        LEFT JOIN Sys_Branches b ON c.BranchId = b.BranchId
        WHERE c.Status=1 AND c.IsDeleted=0 {branch_condition} {date_condition}
        ORDER BY c.AddDate ASC, c.CustomerCode ASC
        """
        
        cursor.execute(query, branch_params + date_params)
        results = cursor.fetchall()
        conn.close()

        print(f"🔍 استعلام قاعدة البيانات: عدد النتائج = {len(results)}")

        if results:
            branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
            branch_name = branch_names.get(branch_id, 'غير محدد')
            days_text = f"{days} ايام" if days else "للكل"

            total_customers = len(results)

            # تجميع العملاء حسب التاريخ
            customers_by_date = {}
            for row in results:
                customer_code = row[0] or 'غير محدد'
                customer_name = row[1] or 'غير محدد'
                phone = row[2] or 'غير محدد'
                branch_name_result = row[3] or 'غير محدد'
                add_date = row[4].strftime('%Y-%m-%d') if row[4] else 'غير محدد'

                if add_date not in customers_by_date:
                    customers_by_date[add_date] = []

                customers_by_date[add_date].append({
                    'code': customer_code,
                    'name': customer_name,
                    'phone': phone,
                    'branch': branch_name_result
                })

            # ترتيب التواريخ من الأقدم للأحدث
            sorted_dates = sorted(customers_by_date.keys())

            # ترتيب العملاء داخل كل تاريخ من الأقدم للأحدث (حسب كود العميل)
            for date in customers_by_date:
                customers_by_date[date] = sorted(customers_by_date[date], key=lambda x: int(x['code']) if x['code'].isdigit() else 0)

            # ملخص مختصر فقط
            dates_summary = f"📈 **إجمالي التواريخ:** {len(sorted_dates)} يوم | **إجمالي العملاء:** {total_customers:,} عميل\n\n"

            # إنشاء التقارير
            reports = []
            current_report = ""
            current_customers_count = 0
            global_customer_number = 1  # ترقيم تسلسلي عام

            header = f"📊 **استعلام عن العملاء في {branch_name} خلال {days_text}**\n\n{dates_summary}"
            current_report = header

            for date in sorted_dates:
                customers_in_date = customers_by_date[date]

                # إضافة عنوان التاريخ
                date_header = f"📅 **{date}** ({len(customers_in_date)} عميل)\n"

                # التحقق من إمكانية إضافة هذا التاريخ والعملاء للرسالة الحالية
                estimated_length = len(current_report) + len(date_header) + (len(customers_in_date) * 70)

                if estimated_length > 1800 and current_customers_count > 0:
                    # إنهاء الرسالة الحالية وبدء رسالة جديدة
                    reports.append(current_report.strip())
                    # رسالة مختصرة للرسائل التالية
                    short_header = f"📊 **استعلام عن العملاء في {branch_name} خلال {days_text}** (تكملة)\n\n"
                    current_report = short_header + date_header
                    current_customers_count = 0
                else:
                    current_report += date_header

                # إضافة العملاء مع ترقيم تسلسلي عام
                for customer in customers_in_date:
                    customer_line = f"  {global_customer_number:03d}. `{customer['code']}` - {customer['name']}\n"
                    customer_line += f"       📞 `{customer['phone']}` | 🏢 {customer['branch']}\n"

                    # التحقق من طول الرسالة قبل إضافة العميل
                    if len(current_report) + len(customer_line) > 3500:
                        # إنهاء الرسالة الحالية وبدء رسالة جديدة
                        reports.append(current_report.strip())
                        short_header = f"📊 **استعلام عن العملاء في {branch_name} خلال {days_text}** (تكملة)\n\n"
                        current_report = short_header + date_header + customer_line
                        current_customers_count = 1
                    else:
                        current_report += customer_line
                        current_customers_count += 1

                    global_customer_number += 1

                current_report += "\n"

            # إضافة الرسالة الأخيرة
            if current_report.strip() != header.strip():
                reports.append(current_report.strip())

            print(f"📊 تم إنشاء {len(reports)} تقرير للإرسال")
            return reports
        else:
            return ["❌ لا توجد بيانات للفترة المحددة"]
            
    except Exception as e:
        return f"❌ حدث خطأ أثناء تحضير التقرير:\n{str(e)}"

def main():
    """الدالة الرئيسية لتشغيل البوت"""
    print("🚀 بدء تشغيل TeERA Bot ")
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)

    try:
        from telegram import Update, ReplyKeyboardMarkup, KeyboardButton
        from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes

        print("✅ تم استيراد مكتبات التليجرام بنجاح")

    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        print("💡 شغل install_packages.bat لتثبيت المكتبات")
        input("اضغط Enter للخروج...")
        return

    # اختبار قاعدة البيانات
    print("🔗 اختبار الاتصال بقاعدة البيانات...")
    try:
        conn = connect_to_db()
        if conn:
            conn.close()
            print("✅ قاعدة البيانات متصلة بنجاح")
        else:
            print("❌ فشل الاتصال بقاعدة البيانات")
            input("اضغط Enter للخروج...")
            return
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {str(e)}")
        input("اضغط Enter للخروج...")
        return

    print("🔄 بدء تشغيل البوت...")
    print("📋 تحضير دوال البوت...")

    # متغيرات حالة المستخدمين
    user_states = {}
    
    # دوال البوت
    async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """دالة البداية"""
        if not update.effective_user or not update.message:
            return

        user_id = update.effective_user.id
        user_name = update.effective_user.first_name or "مستخدم"

        # إعادة تعيين حالة المستخدم
        user_states[user_id] = {'waiting_for': None}
        
        keyboard = [
            [KeyboardButton("🔍 بحث عميل"), KeyboardButton("📊 استعلام عن العملاء")],
            [KeyboardButton("📱 عدد عملاء وسائل التواصل"), KeyboardButton("🌍 عدد العملاء حسب المناطق")],
            [KeyboardButton("📊 عملاء بدون معاينات"), KeyboardButton("👁️ معاينات بدون اجتماعات")],
            [KeyboardButton("🤝 اجتماعات بدون تصميمات"), KeyboardButton("🎨 تصميمات بدون عقود")],
            [KeyboardButton("❌ إنهاء المحادثة")],
        ]
        
        reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=False, resize_keyboard=True)
        
        welcome_msg = f"""🌟 Terra Bot ! 🌟

👤 المستخدم: {user_name}
📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}

اختر أحد الخيارات التالية:"""
        
        await update.message.reply_text(welcome_msg, reply_markup=reply_markup)
        print(f"📨 بدء جلسة جديدة للمستخدم: {user_name} ({user_id})")
    
    async def test_db(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """اختبار قاعدة البيانات"""
        if not update.effective_user or not update.message:
            return

        user_name = update.effective_user.first_name or "مستخدم"
        print(f"📨 طلب اختبار قاعدة البيانات من: {user_name}")

        await update.message.reply_text("🔄 جاري اختبار قاعدة البيانات...")

        result = test_database()
        await update.message.reply_text(result, parse_mode='Markdown')

        # إضافة فحص أسماء الجداول
        await update.message.reply_text("🔄 جاري فحص أسماء الجداول...")
        tables_result = check_table_names()

        # إرسال بدون تنسيق Markdown لتجنب مشاكل الرموز الخاصة
        try:
            await update.message.reply_text(tables_result)
        except Exception as e:
            print(f"❌ خطأ في إرسال معلومات الجداول: {str(e)}")
            await update.message.reply_text("❌ حدث خطأ في عرض معلومات الجداول")

        # إضافة اختبار بنية الجداول
        await update.message.reply_text("🔄 جاري اختبار بنية الجداول...")
        structure_result = test_preview_table_structure()

        try:
            await update.message.reply_text(structure_result)
        except Exception as e:
            print(f"❌ خطأ في إرسال بنية الجداول: {str(e)}")
            await update.message.reply_text("❌ حدث خطأ في عرض بنية الجداول")

    async def search_customer_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """بداية البحث عن عميل"""
        if not update.effective_user or not update.message:
            return

        user_id = update.effective_user.id
        user_name = update.effective_user.first_name or "مستخدم"
        print(f"📨 طلب بحث عميل من: {user_name}")

        user_states[user_id] = {'waiting_for': 'customer_search'}

        await update.message.reply_text(
            "🔍 **بحث عن عميل**\n\n"
            "من فضلك أدخل:\n"
            "• كود العميل\n"
            "• رقم الهاتف الأساسي\n"
            "• رقم الهاتف الفرعي\n\n"
            "مثال: 12345 أو 01012345678\n\n"
            "💡 للإلغاء اكتب: إلغاء أو /start",
            parse_mode='Markdown'
        )

    async def customers_inquiry_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """بداية استعلام العملاء"""
        if not update.effective_user or not update.message:
            return

        user_id = update.effective_user.id
        user_name = update.effective_user.first_name or "مستخدم"
        print(f"📨 طلب استعلام عملاء من: {user_name}")

        user_states[user_id] = {'waiting_for': 'branch_selection_customers'}

        await update.message.reply_text(
            "📍 **من فضلك اختر الفرع:**\n\n"
            "1 - فرع التجمع\n"
            "2 - فرع مدينة نصر\n"
            "3 - للكل",
            parse_mode='Markdown'
        )

    async def regions_inquiry_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """بداية استعلام العملاء حسب المناطق"""
        if not update.effective_user or not update.message:
            return

        user_id = update.effective_user.id
        user_name = update.effective_user.first_name or "مستخدم"
        print(f"📨 طلب استعلام المناطق من: {user_name}")

        user_states[user_id] = {'waiting_for': 'branch_selection_regions'}

        await update.message.reply_text(
            "📍 **من فضلك اختر الفرع:**\n\n"
            "1 - فرع التجمع\n"
            "2 - فرع مدينة نصر\n"
            "3 - للكل",
            parse_mode='Markdown'
        )

    async def social_media_inquiry_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """بداية استعلام العملاء حسب وسائل التواصل"""
        if not update.effective_user or not update.message:
            return

        user_id = update.effective_user.id
        user_name = update.effective_user.first_name or "مستخدم"
        print(f"📨 طلب استعلام وسائل التواصل من: {user_name}")

        user_states[user_id] = {'waiting_for': 'branch_selection_social_media'}

        await update.message.reply_text(
            "📍 **من فضلك اختر الفرع:**\n\n"
            "1 - فرع التجمع\n"
            "2 - فرع مدينة نصر\n"
            "3 - للكل",
            parse_mode='Markdown'
        )

    async def customers_without_previews_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """بداية استعلام العملاء بدون معاينات"""
        if not update.effective_user or not update.message:
            return

        user_id = update.effective_user.id
        user_name = update.effective_user.first_name or "مستخدم"
        print(f"📨 طلب استعلام العملاء بدون معاينات من: {user_name}")

        user_states[user_id] = {'waiting_for': 'branch_selection_no_previews'}

        await update.message.reply_text(
            "📍 **من فضلك اختر الفرع:**\n\n"
            "1 - فرع التجمع\n"
            "2 - فرع مدينة نصر\n"
            "3 - للكل",
            parse_mode='Markdown'
        )

    async def customers_without_designs_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """بداية استعلام العملاء بدون تصميمات"""
        if not update.effective_user or not update.message:
            return

        user_id = update.effective_user.id
        user_name = update.effective_user.first_name or "مستخدم"
        print(f"📨 طلب استعلام العملاء بدون تصميمات من: {user_name}")

        user_states[user_id] = {'waiting_for': 'branch_selection_no_designs'}

        await update.message.reply_text(
            "📍 **من فضلك اختر الفرع:**\n\n"
            "1 - فرع التجمع\n"
            "2 - فرع مدينة نصر\n"
            "3 - للكل",
            parse_mode='Markdown'
        )

    async def customers_without_contracts_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """بداية استعلام العملاء بدون عقود"""
        if not update.effective_user or not update.message:
            return

        user_id = update.effective_user.id
        user_name = update.effective_user.first_name or "مستخدم"
        print(f"📨 طلب استعلام العملاء بدون عقود من: {user_name}")

        user_states[user_id] = {'waiting_for': 'branch_selection_no_contracts'}

        await update.message.reply_text(
            "📍 **من فضلك اختر الفرع:**\n\n"
            "1 - فرع التجمع\n"
            "2 - فرع مدينة نصر\n"
            "3 - للكل",
            parse_mode='Markdown'
        )

    async def previews_without_meetings_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """بداية استعلام معاينات بدون اجتماعات"""
        if not update.effective_user or not update.message:
            return

        user_id = update.effective_user.id
        user_name = update.effective_user.first_name or "مستخدم"
        print(f"📨 طلب استعلام معاينات بدون اجتماعات من: {user_name}")

        user_states[user_id] = {'waiting_for': 'branch_selection_previews_no_meetings'}

        await update.message.reply_text(
            "📍 **من فضلك اختر الفرع:**\n\n"
            "1 - فرع التجمع\n"
            "2 - فرع مدينة نصر\n"
            "3 - للكل",
            parse_mode='Markdown'
        )

    async def meetings_without_designs_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """بداية استعلام اجتماعات بدون تصميمات"""
        if not update.effective_user or not update.message:
            return

        user_id = update.effective_user.id
        user_name = update.effective_user.first_name or "مستخدم"
        print(f"📨 طلب استعلام اجتماعات بدون تصميمات من: {user_name}")

        user_states[user_id] = {'waiting_for': 'branch_selection_meetings_no_designs'}

        await update.message.reply_text(
            "📍 **من فضلك اختر الفرع:**\n\n"
            "1 - فرع التجمع\n"
            "2 - فرع مدينة نصر\n"
            "3 - للكل",
            parse_mode='Markdown'
        )

    async def designs_without_contracts_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """بداية استعلام تصميمات بدون عقود"""
        if not update.effective_user or not update.message:
            return

        user_id = update.effective_user.id
        user_name = update.effective_user.first_name or "مستخدم"
        print(f"📨 طلب استعلام تصميمات بدون عقود من: {user_name}")

        user_states[user_id] = {'waiting_for': 'branch_selection_designs_no_contracts'}

        await update.message.reply_text(
            "📍 **من فضلك اختر الفرع:**\n\n"
            "1 - فرع التجمع\n"
            "2 - فرع مدينة نصر\n"
            "3 - للكل",
            parse_mode='Markdown'
        )

    async def previews_count_regions_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """بداية استعلام أعداد المعاينات حسب المناطق"""
        if not update.effective_user or not update.message:
            return

        user_id = update.effective_user.id
        user_name = update.effective_user.first_name or "مستخدم"
        print(f"📨 طلب استعلام أعداد معاينات المناطق من: {user_name}")

        user_states[user_id] = {'waiting_for': 'branch_selection_previews_count_regions'}

        await update.message.reply_text(
            "📍 **من فضلك اختر الفرع:**\n\n"
            "1 - فرع التجمع\n"
            "2 - فرع مدينة نصر\n"
            "3 - للكل",
            parse_mode='Markdown'
        )

    async def previews_count_social_media_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """بداية استعلام أعداد المعاينات حسب وسائل التواصل"""
        if not update.effective_user or not update.message:
            return

        user_id = update.effective_user.id
        user_name = update.effective_user.first_name or "مستخدم"
        print(f"📨 طلب استعلام أعداد معاينات وسائل التواصل من: {user_name}")

        user_states[user_id] = {'waiting_for': 'branch_selection_previews_count_social_media'}

        await update.message.reply_text(
            "📍 **من فضلك اختر الفرع:**\n\n"
            "1 - فرع التجمع\n"
            "2 - فرع مدينة نصر\n"
            "3 - للكل",
            parse_mode='Markdown'
        )

    async def send_long_message(update, message_text, parse_mode=None):
        """إرسال رسالة طويلة مقسمة إلى أجزاء"""
        max_length = 4000  # ترك مساحة أمان

        if len(message_text) <= max_length:
            # الرسالة قصيرة، أرسلها كما هي
            try:
                await update.message.reply_text(message_text, parse_mode=parse_mode)
            except Exception as e:
                print(f"❌ خطأ في إرسال الرسالة: {str(e)}")
                # محاولة بدون تنسيق
                await update.message.reply_text(message_text)
            return

        # تقسيم الرسالة الطويلة
        lines = message_text.split('\n')
        current_message = ""
        message_count = 1

        for line in lines:
            # إذا إضافة السطر ستتجاوز الحد الأقصى
            if len(current_message + line + '\n') > max_length:
                if current_message:
                    # إرسال الجزء الحالي
                    header = f"📄 **الجزء {message_count}:**\n\n" if message_count > 1 else ""
                    try:
                        await update.message.reply_text(header + current_message, parse_mode=parse_mode)
                    except Exception as e:
                        print(f"❌ خطأ في إرسال الجزء {message_count}: {str(e)}")
                        await update.message.reply_text(header + current_message)

                    await asyncio.sleep(1)  # انتظار ثانية بين الرسائل
                    message_count += 1
                    current_message = ""

            current_message += line + '\n'

        # إرسال الجزء الأخير
        if current_message:
            header = f"📄 **الجزء {message_count}:**\n\n" if message_count > 1 else ""
            try:
                await update.message.reply_text(header + current_message, parse_mode=parse_mode)
            except Exception as e:
                print(f"❌ خطأ في إرسال الجزء الأخير: {str(e)}")
                await update.message.reply_text(header + current_message)

    def is_menu_button(text):
        """التحقق من أن النص هو زر من القائمة الرئيسية"""
        menu_buttons = [
            '🔍 بحث عميل', '📊 استعلام عن العملاء', '📱 عدد عملاء وسائل التواصل',
            '🌍 عدد العملاء حسب المناطق', '📊 عملاء بدون معاينات',
            '👁️ معاينات بدون اجتماعات', '🤝 اجتماعات بدون تصميمات',
            '🎨 تصميمات بدون عقود', '👨‍💼 بحث موظف',
            '📁 تحميل ملفات موظف', '🤝 تحميل اجتماعات', '❌ إنهاء المحادثة',
            '🔙 العودة للقائمة الرئيسية', 'نعم - عرض التفاصيل', 'لا - العودة للقائمة'
        ]
        return text in menu_buttons or text.startswith('/')

    async def handle_text(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة النصوص"""
        if not update.effective_user or not update.message or not update.message.text:
            return

        user_id = update.effective_user.id
        user_input = update.message.text.strip()
        user_name = update.effective_user.first_name or "مستخدم"

        # التحقق من حالة المستخدم
        user_state = user_states.get(user_id, {})
        waiting_for = user_state.get('waiting_for')

        # رسالة تشخيصية
        print(f"🔍 المستخدم {user_name} أدخل: '{user_input}' | الحالة: {waiting_for}")

        # إذا كان المستخدم في حالة انتظار وأدخل زر من القائمة، إعادة توجيه
        if waiting_for and is_menu_button(user_input):
            print(f"🔄 إعادة توجيه من حالة {waiting_for} إلى أمر جديد: {user_input}")
            user_states[user_id] = {'waiting_for': None}
            # إعادة معالجة الرسالة كأمر جديد
            await handle_text(update, context)
            return

        if waiting_for == 'customer_search':
            # التحقق من أوامر الخروج أو الزر
            if user_input.lower() in ['الغاء', 'إلغاء', 'خروج', 'cancel', '/start'] or user_input == '🔙 العودة للقائمة الرئيسية':
                user_states[user_id] = {'waiting_for': None}

                # إعادة عرض القائمة الرئيسية
                from telegram import ReplyKeyboardMarkup, KeyboardButton
                keyboard = [
                    [KeyboardButton("🔍 بحث عميل"), KeyboardButton("📊 استعلام عن العملاء")],
                    [KeyboardButton("📱 عدد عملاء وسائل التواصل"), KeyboardButton("🌍 عدد العملاء حسب المناطق")],
                    [KeyboardButton("📊 عملاء بدون معاينات"), KeyboardButton("👁️ معاينات بدون اجتماعات")],
                    [KeyboardButton("🤝 اجتماعات بدون تصميمات"), KeyboardButton("🎨 تصميمات بدون عقود")],
                    [KeyboardButton("❌ إنهاء المحادثة")],
                ]
                reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=False, resize_keyboard=True)

                await update.message.reply_text("🔙 **تم العودة للقائمة الرئيسية**\n\nاختر أحد الخيارات:",
                                               parse_mode='Markdown', reply_markup=reply_markup)
                return

            print(f"🔍 بحث عن عميل: {user_input} من: {user_name}")
            await update.message.reply_text("🔄 جاري البحث...")

            try:
                result = search_customer_in_db(user_input)
                print(f"🔍 نتيجة البحث: {result[:100]}...")  # طباعة أول 100 حرف للتشخيص

                # محاولة إرسال بدون Markdown أولاً لتجنب مشاكل التنسيق
                try:
                    await update.message.reply_text(result)
                    print("✅ تم إرسال نتيجة البحث بنجاح")
                except Exception as e:
                    print(f"❌ خطأ في إرسال الرسالة: {str(e)}")
                    # محاولة إرسال رسالة خطأ مبسطة
                    try:
                        await update.message.reply_text("❌ حدث خطأ في عرض بيانات العميل")
                    except:
                        print("❌ فشل في إرسال رسالة الخطأ أيضاً")

            except Exception as e:
                print(f"❌ خطأ في دالة البحث: {str(e)}")
                try:
                    await update.message.reply_text("❌ حدث خطأ أثناء البحث في قاعدة البيانات")
                except:
                    print("❌ فشل في إرسال رسالة خطأ البحث")

            # إذا تم العثور على العميل، اطلب البحث التالي
            if "📋 **بيانات العميل**" in result:
                import asyncio
                await asyncio.sleep(1)  # انتظار قصير

                # إنشاء لوحة مفاتيح مع خيارات
                from telegram import ReplyKeyboardMarkup, KeyboardButton
                keyboard = [
                    [KeyboardButton("🔙 العودة للقائمة الرئيسية")]
                ]
                reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=True, resize_keyboard=True)

                next_search_msg = (
                    "🔍 **للبحث عن عميل آخر:**\n"
                    "أدخل كود العميل أو رقم الهاتف\n\n"
                    "🔙 **أو اضغط الزر أدناه للعودة للقائمة الرئيسية**"
                )
                await update.message.reply_text(next_search_msg, parse_mode='Markdown', reply_markup=reply_markup)
                print(f"✅ تم العثور على عميل للمستخدم: {user_name} - في انتظار البحث التالي")
                # الحفاظ على حالة البحث
                user_states[user_id] = {'waiting_for': 'customer_search'}

        elif waiting_for == 'branch_selection_customers':
            if user_input in ['1', '2', '3']:
                branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
                selected_branch = branch_names[user_input]

                await update.message.reply_text(f"✅ تم اختيار {user_input} {selected_branch}")

                user_states[user_id] = {
                    'waiting_for': 'days_customers',
                    'selected_branch': user_input,
                    'branch_name': selected_branch
                }

                await update.message.reply_text(
                    f"📅 من فضلك ادخل عدد ايام مده البحث المختاره او للكل اضغط # لفرع: {selected_branch}"
                )
            else:
                await update.message.reply_text("⚠️ من فضلك اختر رقم صحيح: 1 أو 2 أو 3")

        elif waiting_for == 'days_customers':
            # التحقق من زر العودة للقائمة
            if user_input == '🔙 العودة للقائمة الرئيسية':
                user_states[user_id] = {'waiting_for': None}

                # إعادة عرض القائمة الرئيسية
                from telegram import ReplyKeyboardMarkup, KeyboardButton
                keyboard = [
                    [KeyboardButton("🔍 بحث عميل"), KeyboardButton("📊 استعلام عن العملاء")],
                    [KeyboardButton("📱 عدد عملاء وسائل التواصل"), KeyboardButton("🌍 عدد العملاء حسب المناطق")],
                    [KeyboardButton("📊 عملاء بدون معاينات"), KeyboardButton("👁️ معاينات بدون اجتماعات")],
                    [KeyboardButton("🤝 اجتماعات بدون تصميمات"), KeyboardButton("🎨 تصميمات بدون عقود")],
                    [KeyboardButton("❌ إنهاء المحادثة")],
                ]
                reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=False, resize_keyboard=True)

                await update.message.reply_text("🔙 **تم العودة للقائمة الرئيسية**\n\nاختر أحد الخيارات:",
                                               parse_mode='Markdown', reply_markup=reply_markup)
                return

            if user_input == '#':
                days_value = None
                days_text = "للكل"
            else:
                try:
                    days_value = int(user_input)
                    days_text = f"{days_value} ايام"
                except ValueError:
                    await update.message.reply_text("⚠️ من فضلك أدخل رقم صحيح أو # للكل")
                    return

            branch_id = user_state.get('selected_branch', '1')
            branch_name = user_state.get('branch_name', 'غير محدد')

            await update.message.reply_text("🔄 جاري تحضير التقرير...")

            results = get_customers_list(branch_id, days_value)
            print(f"🔍 نتائج الاستعلام: نوع={type(results)}, عدد={len(results) if isinstance(results, list) else 'غير قائمة'}")

            # التحقق من نوع النتيجة
            if isinstance(results, str):
                # إذا كانت النتيجة نص (خطأ أو لا توجد بيانات)
                await update.message.reply_text(results, parse_mode='Markdown')

                # إذا لم توجد بيانات، اعطي خيارات
                if "❌ لا توجد بيانات" in results:
                    from telegram import ReplyKeyboardMarkup, KeyboardButton
                    keyboard = [
                        [KeyboardButton("🔙 العودة للقائمة الرئيسية")]
                    ]
                    reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=True, resize_keyboard=True)

                    retry_msg = (
                        "🔄 **للبحث مرة أخرى:**\n"
                        "ادخل المدة الزمنية\n\n"
                        "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                    )
                    await update.message.reply_text(retry_msg, parse_mode='Markdown')

                    # الاحتفاظ بحالة إعادة المحاولة
                    user_states[user_id] = {
                        'waiting_for': 'days_customers',
                        'selected_branch': user_state.get('selected_branch'),
                        'branch_name': user_state.get('branch_name')
                    }
                    print(f"⚠️ لا توجد بيانات للمستخدم: {user_name} - في انتظار مدة جديدة")
            elif isinstance(results, list):
                # إذا كانت النتيجة قائمة (صحيح)
                print(f"📊 سيتم إرسال {len(results)} رسالة")

                import asyncio

                # إرسال رسالة تقدم إذا كان هناك أكثر من 10 رسائل
                if len(results) > 10:
                    progress_msg = f"📊 **سيتم إرسال {len(results)} رسالة**\n⏳ **الوقت المتوقع:** {len(results) * 3} ثانية تقريباً"
                    await update.message.reply_text(progress_msg, parse_mode='Markdown')
                    await asyncio.sleep(2)

                for i, report in enumerate(results):
                    print(f"📤 إرسال رسالة {i+1}/{len(results)}")
                    try:
                        await send_long_message(update, report, parse_mode='Markdown')
                        print(f"✅ تم إرسال الرسالة {i+1} بنجاح")
                    except Exception as e:
                        print(f"❌ خطأ في إرسال الرسالة {i+1}: {str(e)}")
                        # محاولة إرسال بدون تنسيق Markdown
                        try:
                            await send_long_message(update, report)
                            print(f"✅ تم إرسال الرسالة {i+1} بدون تنسيق")
                        except Exception as e2:
                            print(f"❌ فشل نهائي في إرسال الرسالة {i+1}: {str(e2)}")
                            continue

                    # فاصل زمني 3 ثوانٍ بين الرسائل
                    if i < len(results) - 1:
                        print(f"⏳ انتظار 3 ثوانٍ قبل الرسالة التالية...")
                        await asyncio.sleep(3)

                print(f"✅ تم إرسال جميع الرسائل: {len(results)} رسالة")

                # رسالة تأكيد نهائية مع خيارات
                try:
                    await asyncio.sleep(2)  # انتظار قصير قبل رسالة التأكيد
                    confirmation_msg = f"✅ **تم تنفيذ الاستعلام بنجاح**\n\n"
                    confirmation_msg += f"📊 **تم إرسال:** {len(results)} رسالة\n"
                    confirmation_msg += f"⏰ **وقت الانتهاء:** {datetime.now().strftime('%H:%M:%S')}"

                    await update.message.reply_text(confirmation_msg, parse_mode='Markdown')



                    options_msg = (
                        "🔄 **للبحث مرة أخرى:**\n"
                        "ادخل المدة الزمنية\n\n"
                        "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                    )
                    await update.message.reply_text(options_msg, parse_mode='Markdown')

                    # العودة لحالة انتظار المدة مع الاحتفاظ بالفرع
                    user_states[user_id] = {
                        'waiting_for': 'days_customers',
                        'selected_branch': user_state.get('selected_branch'),
                        'branch_name': user_state.get('branch_name')
                    }

                    print("✅ تم إرسال رسالة التأكيد والخيارات")
                except Exception as e:
                    print(f"❌ خطأ في إرسال رسالة التأكيد: {str(e)}")
                    user_states[user_id] = {'waiting_for': None}
            else:
                await update.message.reply_text("❌ خطأ غير متوقع في النتائج")
                user_states[user_id] = {'waiting_for': None}

            print(f"✅ تم تنفيذ تقرير العملاء للمستخدم: {user_name}")

        elif waiting_for == 'branch_selection_regions':
            if user_input in ['1', '2', '3']:
                branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
                selected_branch = branch_names[user_input]

                await update.message.reply_text(f"✅ تم اختيار {user_input} {selected_branch}")

                user_states[user_id] = {
                    'waiting_for': 'days_regions',
                    'selected_branch': user_input,
                    'branch_name': selected_branch
                }

                await update.message.reply_text(
                    f"📅 من فضلك ادخل عدد ايام مده البحث المختاره او للكل اضغط # لفرع: {selected_branch}"
                )
            else:
                await update.message.reply_text("⚠️ من فضلك اختر رقم صحيح: 1 أو 2 أو 3")

        elif waiting_for == 'days_regions':
            # التحقق من زر العودة للقائمة
            if user_input == '🔙 العودة للقائمة الرئيسية':
                user_states[user_id] = {'waiting_for': None}

                # إعادة عرض القائمة الرئيسية
                from telegram import ReplyKeyboardMarkup, KeyboardButton
                keyboard = [
                    [KeyboardButton("🔍 بحث عميل"), KeyboardButton("📊 استعلام عن العملاء")],
                    [KeyboardButton("📱 عدد عملاء وسائل التواصل"), KeyboardButton("🌍 عدد العملاء حسب المناطق")],
                    [KeyboardButton("📊 عملاء بدون معاينات"), KeyboardButton("👁️ معاينات بدون اجتماعات")],
                    [KeyboardButton("🤝 اجتماعات بدون تصميمات"), KeyboardButton("🎨 تصميمات بدون عقود")],
                    [KeyboardButton("❌ إنهاء المحادثة")],
                ]
                reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=False, resize_keyboard=True)

                await update.message.reply_text("🔙 **تم العودة للقائمة الرئيسية**\n\nاختر أحد الخيارات:",
                                               parse_mode='Markdown', reply_markup=reply_markup)
                return

            if user_input == '#':
                days_value = None
            else:
                try:
                    days_value = int(user_input)
                except ValueError:
                    await update.message.reply_text("⚠️ من فضلك أدخل رقم صحيح أو # للكل")
                    return

            branch_id = user_state.get('selected_branch', '1')

            await update.message.reply_text("🔄 جاري تحضير تقرير المناطق...")

            result = get_customers_by_regions(branch_id, days_value)
            await send_long_message(update, result, parse_mode='Markdown')

            # إذا لم توجد بيانات، اعطي خيارات
            if "❌ لا توجد بيانات" in result:
                from telegram import ReplyKeyboardMarkup, KeyboardButton
                keyboard = [
                    [KeyboardButton("🔙 العودة للقائمة الرئيسية")]
                ]
                reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=True, resize_keyboard=True)

                retry_msg = (
                    "🔄 **للبحث مرة أخرى:**\n"
                    "ادخل المدة الزمنية\n\n"
                    "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                )
                await update.message.reply_text(retry_msg, parse_mode='Markdown')

                # الاحتفاظ بحالة إعادة المحاولة
                user_states[user_id] = {
                    'waiting_for': 'days_regions',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name')
                }
                print(f"⚠️ لا توجد بيانات للمستخدم: {user_name} - في انتظار مدة جديدة")
            else:


                options_msg = (
                    "🔄 **للبحث مرة أخرى:**\n"
                    "ادخل المدة الزمنية\n\n"
                    "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                )
                await update.message.reply_text(options_msg, parse_mode='Markdown')

                # العودة لحالة انتظار المدة مع الاحتفاظ بالفرع
                user_states[user_id] = {
                    'waiting_for': 'days_regions',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name')
                }
                print(f"✅ تم تنفيذ تقرير المناطق للمستخدم: {user_name}")

        elif waiting_for == 'branch_selection_social_media':
            if user_input in ['1', '2', '3']:
                branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
                selected_branch = branch_names[user_input]

                await update.message.reply_text(f"✅ تم اختيار {user_input} {selected_branch}")

                user_states[user_id] = {
                    'waiting_for': 'days_social_media',
                    'selected_branch': user_input,
                    'branch_name': selected_branch
                }

                await update.message.reply_text(
                    f"📅 من فضلك ادخل عدد ايام مده البحث المختاره او للكل اضغط # لفرع: {selected_branch}"
                )
            else:
                await update.message.reply_text("⚠️ من فضلك اختر رقم صحيح: 1 أو 2 أو 3")

        elif waiting_for == 'days_social_media':
            # التحقق من زر العودة للقائمة
            if user_input == '🔙 العودة للقائمة الرئيسية':
                user_states[user_id] = {'waiting_for': None}

                # إعادة عرض القائمة الرئيسية
                from telegram import ReplyKeyboardMarkup, KeyboardButton
                keyboard = [
                    [KeyboardButton("🔍 بحث عميل"), KeyboardButton("📊 استعلام عن العملاء")],
                    [KeyboardButton("📱 عدد عملاء وسائل التواصل"), KeyboardButton("🌍 عدد العملاء حسب المناطق")],
                    [KeyboardButton("📊 عملاء بدون معاينات"), KeyboardButton("👁️ معاينات بدون اجتماعات")],
                    [KeyboardButton("🤝 اجتماعات بدون تصميمات"), KeyboardButton("🎨 تصميمات بدون عقود")],
                    [KeyboardButton("❌ إنهاء المحادثة")],
                ]
                reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=False, resize_keyboard=True)

                await update.message.reply_text("🔙 **تم العودة للقائمة الرئيسية**\n\nاختر أحد الخيارات:",
                                               parse_mode='Markdown', reply_markup=reply_markup)
                return

            if user_input == '#':
                days_value = None
            else:
                try:
                    days_value = int(user_input)
                except ValueError:
                    await update.message.reply_text("⚠️ من فضلك أدخل رقم صحيح أو # للكل")
                    return

            branch_id = user_state.get('selected_branch', '1')

            await update.message.reply_text("🔄 جاري تحضير تقرير وسائل التواصل...")

            result = get_customers_by_social_media(branch_id, days_value)
            await send_long_message(update, result, parse_mode='Markdown')

            # إذا لم توجد بيانات، اعطي خيارات
            if "❌ لا توجد بيانات" in result:
                from telegram import ReplyKeyboardMarkup, KeyboardButton
                keyboard = [
                    [KeyboardButton("🔙 العودة للقائمة الرئيسية")]
                ]
                reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=True, resize_keyboard=True)

                retry_msg = (
                    "🔄 **للبحث مرة أخرى:**\n"
                    "ادخل المدة الزمنية\n\n"
                    "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                )
                await update.message.reply_text(retry_msg, parse_mode='Markdown')

                # الاحتفاظ بحالة إعادة المحاولة
                user_states[user_id] = {
                    'waiting_for': 'days_social_media',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name')
                }
                print(f"⚠️ لا توجد بيانات للمستخدم: {user_name} - في انتظار مدة جديدة")
            else:


                options_msg = (
                    "🔄 **للبحث مرة أخرى:**\n"
                    "ادخل المدة الزمنية\n\n"
                    "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                )
                await update.message.reply_text(options_msg, parse_mode='Markdown')

                # العودة لحالة انتظار المدة مع الاحتفاظ بالفرع
                user_states[user_id] = {
                    'waiting_for': 'days_social_media',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name')
                }
                print(f"✅ تم تنفيذ تقرير وسائل التواصل للمستخدم: {user_name}")

        # معالجة العملاء بدون معاينات
        elif waiting_for == 'branch_selection_no_previews':
            if user_input in ['1', '2', '3']:
                branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
                selected_branch = branch_names[user_input]

                await update.message.reply_text(f"✅ تم اختيار {user_input} {selected_branch}")

                user_states[user_id] = {
                    'waiting_for': 'days_no_previews',
                    'selected_branch': user_input,
                    'branch_name': selected_branch
                }

                await update.message.reply_text(
                    f"📅 من فضلك ادخل عدد ايام مده البحث المختاره او للكل اضغط # لفرع: {selected_branch}"
                )
            else:
                await update.message.reply_text("⚠️ من فضلك اختر رقم صحيح: 1 أو 2 أو 3")

        elif waiting_for == 'days_no_previews':
            if user_input == '#':
                days_value = None
            else:
                try:
                    days_value = int(user_input)
                except ValueError:
                    await update.message.reply_text("⚠️ من فضلك أدخل رقم صحيح أو # للكل")
                    return

            branch_id = user_state.get('selected_branch', '1')

            await update.message.reply_text("🔄 جاري تحضير إحصائيات العملاء بدون معاينات...")

            # الحصول على الأعداد أولاً
            result, count = get_customers_without_previews_count(branch_id, days_value)

            if isinstance(result, str) and count == 0:
                await update.message.reply_text(result)
                retry_msg = (
                    "🔄 **للبحث مرة أخرى:**\n"
                    "ادخل المدة الزمنية\n\n"
                    "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                )
                await update.message.reply_text(retry_msg, parse_mode='Markdown')

                user_states[user_id] = {
                    'waiting_for': 'days_no_previews',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name')
                }
                print(f"⚠️ لا توجد بيانات للمستخدم: {user_name} - في انتظار مدة جديدة")
            else:
                # عرض الإحصائيات
                await send_long_message(update, result, parse_mode='Markdown')

                # السؤال عن التفاصيل
                from telegram import ReplyKeyboardMarkup, KeyboardButton
                keyboard = [
                    [KeyboardButton("📄 تصدير التقرير مفصل"), KeyboardButton("لا - العودة للقائمة")]
                ]
                reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=True, resize_keyboard=True)

                details_msg = f"📋 **هل تريد تصدير تقرير مفصل بأسماء العملاء؟**\n\n📊 **العدد:** {count} عميل"
                await update.message.reply_text(details_msg, parse_mode='Markdown', reply_markup=reply_markup)

                # حفظ البيانات لاستخدامها في التفاصيل
                user_states[user_id] = {
                    'waiting_for': 'details_confirmation_no_previews',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name'),
                    'days_value': days_value,
                    'count': count
                }
                print(f"✅ تم عرض إحصائيات العملاء بدون معاينات للمستخدم: {user_name}")

        elif waiting_for == 'details_confirmation_no_previews':
            if user_input == "📄 تصدير التقرير مفصل" or "تصدير" in user_input and "مفصل" in user_input:
                branch_id = user_state.get('selected_branch', '1')
                days_value = user_state.get('days_value')
                branch_name = user_state.get('branch_name', 'غير محدد')

                await update.message.reply_text("🔄 جاري إنشاء ملف PDF...")

                try:
                    # الحصول على البيانات
                    customers_data, status_msg = get_customers_without_previews_for_pdf(branch_id, days_value)

                    if customers_data:
                        # إنشاء PDF
                        days_text = f"{days_value} ايام" if days_value else "للكل"
                        report_title = "تقرير العملاء بدون معاينات"

                        pdf_path, pdf_status = create_customers_pdf(
                            customers_data,
                            report_title,
                            branch_name,
                            days_text
                        )

                        if pdf_path and os.path.exists(pdf_path):
                            # إرسال الملف
                            with open(pdf_path, 'rb') as pdf_file:
                                await update.message.reply_document(
                                    document=pdf_file,
                                    filename=os.path.basename(pdf_path),
                                    caption=f"📄 **{report_title}**\n\n🏢 **الفرع:** {branch_name}\n📅 **الفترة:** {days_text}\n👥 **العدد:** {len(customers_data)} عميل",
                                    parse_mode='Markdown'
                                )

                            # حذف الملف بعد الإرسال
                            try:
                                os.remove(pdf_path)
                            except:
                                pass

                            await update.message.reply_text("✅ **تم إرسال ملف PDF بنجاح!**", parse_mode='Markdown')
                            print(f"✅ تم إرسال PDF للمستخدم: {user_name}")
                        else:
                            await update.message.reply_text(pdf_status)
                            print(f"❌ فشل إنشاء PDF للمستخدم: {user_name}")
                    else:
                        await update.message.reply_text(status_msg)
                        print(f"⚠️ لا توجد بيانات PDF للمستخدم: {user_name}")

                except Exception as e:
                    error_msg = f"❌ حدث خطأ أثناء إنشاء PDF: {str(e)}"
                    await update.message.reply_text(error_msg)
                    print(f"❌ خطأ في PDF: {str(e)}")

                # العودة للقائمة الرئيسية
                user_states[user_id] = {'waiting_for': None}
                await start(update, context)

            elif user_input == "لا - العودة للقائمة" or "لا" in user_input or "عودة" in user_input:
                # العودة للقائمة الرئيسية
                user_states[user_id] = {'waiting_for': None}
                await start(update, context)
                print(f"🔙 عودة للقائمة الرئيسية للمستخدم: {user_name}")
            else:
                await update.message.reply_text("⚠️ من فضلك اختر أحد الخيارين المتاحين")

        # معالجة العملاء بدون تصميمات
        elif waiting_for == 'branch_selection_no_designs':
            if user_input in ['1', '2', '3']:
                branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
                selected_branch = branch_names[user_input]

                await update.message.reply_text(f"✅ تم اختيار {user_input} {selected_branch}")

                user_states[user_id] = {
                    'waiting_for': 'days_no_designs',
                    'selected_branch': user_input,
                    'branch_name': selected_branch
                }

                await update.message.reply_text(
                    f"📅 من فضلك ادخل عدد ايام مده البحث المختاره او للكل اضغط # لفرع: {selected_branch}"
                )
            else:
                await update.message.reply_text("⚠️ من فضلك اختر رقم صحيح: 1 أو 2 أو 3")

        elif waiting_for == 'days_no_designs':
            if user_input == '#':
                days_value = None
            else:
                try:
                    days_value = int(user_input)
                except ValueError:
                    await update.message.reply_text("⚠️ من فضلك أدخل رقم صحيح أو # للكل")
                    return

            branch_id = user_state.get('selected_branch', '1')

            await update.message.reply_text("🔄 جاري تحضير تقرير العملاء بدون تصميمات...")

            result = get_customers_without_designs(branch_id, days_value)
            await send_long_message(update, result)

            # إذا لم توجد بيانات، اعطي خيارات
            if "❌ لا توجد بيانات" in result:
                retry_msg = (
                    "🔄 **للبحث مرة أخرى:**\n"
                    "ادخل المدة الزمنية\n\n"
                    "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                )
                await update.message.reply_text(retry_msg, parse_mode='Markdown')

                user_states[user_id] = {
                    'waiting_for': 'days_no_designs',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name')
                }
                print(f"⚠️ لا توجد بيانات للمستخدم: {user_name} - في انتظار مدة جديدة")
            else:
                options_msg = (
                    "🔄 **للبحث مرة أخرى:**\n"
                    "ادخل المدة الزمنية\n\n"
                    "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                )
                await update.message.reply_text(options_msg, parse_mode='Markdown')

                user_states[user_id] = {
                    'waiting_for': 'days_no_designs',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name')
                }
                print(f"✅ تم تنفيذ تقرير العملاء بدون تصميمات للمستخدم: {user_name}")

        # معالجة العملاء بدون عقود
        elif waiting_for == 'branch_selection_no_contracts':
            if user_input in ['1', '2', '3']:
                branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
                selected_branch = branch_names[user_input]

                await update.message.reply_text(f"✅ تم اختيار {user_input} {selected_branch}")

                user_states[user_id] = {
                    'waiting_for': 'days_no_contracts',
                    'selected_branch': user_input,
                    'branch_name': selected_branch
                }

                await update.message.reply_text(
                    f"📅 من فضلك ادخل عدد ايام مده البحث المختاره او للكل اضغط # لفرع: {selected_branch}"
                )
            else:
                await update.message.reply_text("⚠️ من فضلك اختر رقم صحيح: 1 أو 2 أو 3")

        elif waiting_for == 'days_no_contracts':
            if user_input == '#':
                days_value = None
            else:
                try:
                    days_value = int(user_input)
                except ValueError:
                    await update.message.reply_text("⚠️ من فضلك أدخل رقم صحيح أو # للكل")
                    return

            branch_id = user_state.get('selected_branch', '1')

            await update.message.reply_text("🔄 جاري تحضير تقرير العملاء بدون عقود...")

            result = get_customers_without_contracts(branch_id, days_value)
            await send_long_message(update, result)

            # إذا لم توجد بيانات، اعطي خيارات
            if "❌ لا توجد بيانات" in result:
                retry_msg = (
                    "🔄 **للبحث مرة أخرى:**\n"
                    "ادخل المدة الزمنية\n\n"
                    "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                )
                await update.message.reply_text(retry_msg, parse_mode='Markdown')

                user_states[user_id] = {
                    'waiting_for': 'days_no_contracts',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name')
                }
                print(f"⚠️ لا توجد بيانات للمستخدم: {user_name} - في انتظار مدة جديدة")
            else:
                options_msg = (
                    "🔄 **للبحث مرة أخرى:**\n"
                    "ادخل المدة الزمنية\n\n"
                    "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                )
                await update.message.reply_text(options_msg, parse_mode='Markdown')

                user_states[user_id] = {
                    'waiting_for': 'days_no_contracts',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name')
                }
                print(f"✅ تم تنفيذ تقرير العملاء بدون عقود للمستخدم: {user_name}")

        # معالجة المعاينات بدون اجتماعات
        elif waiting_for == 'branch_selection_previews_no_meetings':
            if user_input in ['1', '2', '3']:
                branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
                selected_branch = branch_names[user_input]

                await update.message.reply_text(f"✅ تم اختيار {user_input} {selected_branch}")

                user_states[user_id] = {
                    'waiting_for': 'days_previews_no_meetings',
                    'selected_branch': user_input,
                    'branch_name': selected_branch
                }

                await update.message.reply_text(
                    f"📅 من فضلك ادخل عدد ايام مده البحث المختاره او للكل اضغط # لفرع: {selected_branch}"
                )
            else:
                await update.message.reply_text("⚠️ من فضلك اختر رقم صحيح: 1 أو 2 أو 3")

        elif waiting_for == 'days_previews_no_meetings':
            if user_input == '#':
                days_value = None
            else:
                try:
                    days_value = int(user_input)
                except ValueError:
                    await update.message.reply_text("⚠️ من فضلك أدخل رقم صحيح أو # للكل")
                    return

            branch_id = user_state.get('selected_branch', '1')

            await update.message.reply_text("🔄 جاري تحضير إحصائيات العملاء لهم معاينات بدون اجتماعات...")

            # الحصول على الأعداد أولاً
            result, count = get_customers_with_previews_no_meetings_count(branch_id, days_value)

            if isinstance(result, str) and count == 0:
                await update.message.reply_text(result)
                retry_msg = (
                    "🔄 **للبحث مرة أخرى:**\n"
                    "ادخل المدة الزمنية\n\n"
                    "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                )
                await update.message.reply_text(retry_msg, parse_mode='Markdown')

                user_states[user_id] = {
                    'waiting_for': 'days_previews_no_meetings',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name')
                }
                print(f"⚠️ لا توجد بيانات للمستخدم: {user_name} - في انتظار مدة جديدة")
            else:
                # عرض الإحصائيات
                await send_long_message(update, result, parse_mode='Markdown')

                # السؤال عن التفاصيل
                from telegram import ReplyKeyboardMarkup, KeyboardButton
                keyboard = [
                    [KeyboardButton("نعم - عرض التفاصيل"), KeyboardButton("لا - العودة للقائمة")]
                ]
                reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=True, resize_keyboard=True)

                details_msg = f"📋 **هل تريد عرض تفاصيل العملاء ببياناتهم؟**\n\n📊 **العدد:** {count} عميل"
                await update.message.reply_text(details_msg, parse_mode='Markdown', reply_markup=reply_markup)

                # حفظ البيانات لاستخدامها في التفاصيل
                user_states[user_id] = {
                    'waiting_for': 'details_confirmation_previews_no_meetings',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name'),
                    'days_value': days_value,
                    'count': count
                }
                print(f"✅ تم عرض إحصائيات العملاء لهم معاينات بدون اجتماعات للمستخدم: {user_name}")

        elif waiting_for == 'details_confirmation_previews_no_meetings':
            if user_input == "نعم - عرض التفاصيل":
                branch_id = user_state.get('selected_branch', '1')
                days_value = user_state.get('days_value')

                await update.message.reply_text("🔄 جاري تحضير تفاصيل العملاء...")

                try:
                    # الحصول على التفاصيل
                    result = get_customers_with_previews_no_meetings(branch_id, days_value)
                    print(f"📋 نتيجة تفاصيل المعاينات: {len(result)} حرف")

                    if result and not result.startswith("❌"):
                        await send_long_message(update, result)
                        print(f"✅ تم إرسال تفاصيل المعاينات بنجاح للمستخدم: {user_name}")
                    else:
                        await update.message.reply_text(result or "❌ لا توجد تفاصيل متاحة")
                        print(f"⚠️ لا توجد تفاصيل المعاينات للمستخدم: {user_name}")

                except Exception as e:
                    error_msg = f"❌ حدث خطأ أثناء تحضير التفاصيل: {str(e)}"
                    await update.message.reply_text(error_msg)
                    print(f"❌ خطأ في تفاصيل المعاينات: {str(e)}")

                # خيارات بعد عرض التفاصيل
                options_msg = (
                    "🔄 **للبحث مرة أخرى:**\n"
                    "ادخل المدة الزمنية\n\n"
                    "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                )
                await update.message.reply_text(options_msg, parse_mode='Markdown')

                user_states[user_id] = {
                    'waiting_for': 'days_previews_no_meetings',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name')
                }
                print(f"✅ تم عرض تفاصيل العملاء لهم معاينات بدون اجتماعات للمستخدم: {user_name}")

            elif user_input == "لا - العودة للقائمة":
                # العودة للقائمة الرئيسية
                user_states[user_id] = {'waiting_for': None}
                await start(update, context)
                print(f"🔙 عودة للقائمة الرئيسية للمستخدم: {user_name}")
            else:
                await update.message.reply_text("⚠️ من فضلك اختر أحد الخيارين المتاحين")

            # إذا لم توجد بيانات، اعطي خيارات
            if "❌ لا توجد بيانات" in result:
                retry_msg = (
                    "🔄 **للبحث مرة أخرى:**\n"
                    "ادخل المدة الزمنية\n\n"
                    "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                )
                await update.message.reply_text(retry_msg, parse_mode='Markdown')

                user_states[user_id] = {
                    'waiting_for': 'days_previews_no_meetings',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name')
                }
                print(f"⚠️ لا توجد بيانات للمستخدم: {user_name} - في انتظار مدة جديدة")
            else:
                options_msg = (
                    "🔄 **للبحث مرة أخرى:**\n"
                    "ادخل المدة الزمنية\n\n"
                    "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                )
                await update.message.reply_text(options_msg, parse_mode='Markdown')

                user_states[user_id] = {
                    'waiting_for': 'days_previews_no_meetings',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name')
                }
                print(f"✅ تم تنفيذ تقرير المعاينات بدون اجتماعات للمستخدم: {user_name}")

        # معالجة أعداد المعاينات حسب المناطق
        elif waiting_for == 'branch_selection_previews_count_regions':
            if user_input in ['1', '2', '3']:
                branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
                selected_branch = branch_names[user_input]

                await update.message.reply_text(f"✅ تم اختيار {user_input} {selected_branch}")

                user_states[user_id] = {
                    'waiting_for': 'days_previews_count_regions',
                    'selected_branch': user_input,
                    'branch_name': selected_branch
                }

                await update.message.reply_text(
                    f"📅 من فضلك ادخل عدد ايام مده البحث المختاره او للكل اضغط # لفرع: {selected_branch}"
                )
            else:
                await update.message.reply_text("⚠️ من فضلك اختر رقم صحيح: 1 أو 2 أو 3")

        elif waiting_for == 'days_previews_count_regions':
            if user_input == '#':
                days_value = None
            else:
                try:
                    days_value = int(user_input)
                except ValueError:
                    await update.message.reply_text("⚠️ من فضلك أدخل رقم صحيح أو # للكل")
                    return

            branch_id = user_state.get('selected_branch', '1')

            await update.message.reply_text("🔄 جاري تحضير تقرير أعداد المعاينات حسب المناطق...")

            result = get_previews_count_by_regions(branch_id, days_value)
            await send_long_message(update, result, parse_mode='Markdown')

            # إذا لم توجد بيانات، اعطي خيارات
            if "❌ لا توجد بيانات" in result:
                retry_msg = (
                    "🔄 **للبحث مرة أخرى:**\n"
                    "ادخل المدة الزمنية\n\n"
                    "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                )
                await update.message.reply_text(retry_msg, parse_mode='Markdown')

                user_states[user_id] = {
                    'waiting_for': 'days_previews_count_regions',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name')
                }
                print(f"⚠️ لا توجد بيانات للمستخدم: {user_name} - في انتظار مدة جديدة")
            else:
                options_msg = (
                    "🔄 **للبحث مرة أخرى:**\n"
                    "ادخل المدة الزمنية\n\n"
                    "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                )
                await update.message.reply_text(options_msg, parse_mode='Markdown')

                user_states[user_id] = {
                    'waiting_for': 'days_previews_count_regions',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name')
                }
                print(f"✅ تم تنفيذ تقرير أعداد المعاينات حسب المناطق للمستخدم: {user_name}")

        # معالجة أعداد المعاينات حسب وسائل التواصل
        elif waiting_for == 'branch_selection_previews_count_social_media':
            if user_input in ['1', '2', '3']:
                branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
                selected_branch = branch_names[user_input]

                await update.message.reply_text(f"✅ تم اختيار {user_input} {selected_branch}")

                user_states[user_id] = {
                    'waiting_for': 'days_previews_count_social_media',
                    'selected_branch': user_input,
                    'branch_name': selected_branch
                }

                await update.message.reply_text(
                    f"📅 من فضلك ادخل عدد ايام مده البحث المختاره او للكل اضغط # لفرع: {selected_branch}"
                )
            else:
                await update.message.reply_text("⚠️ من فضلك اختر رقم صحيح: 1 أو 2 أو 3")

        elif waiting_for == 'days_previews_count_social_media':
            if user_input == '#':
                days_value = None
            else:
                try:
                    days_value = int(user_input)
                except ValueError:
                    await update.message.reply_text("⚠️ من فضلك أدخل رقم صحيح أو # للكل")
                    return

            branch_id = user_state.get('selected_branch', '1')

            await update.message.reply_text("🔄 جاري تحضير تقرير أعداد المعاينات حسب وسائل التواصل...")

            result = get_previews_count_by_social_media(branch_id, days_value)
            await send_long_message(update, result, parse_mode='Markdown')

            # إذا لم توجد بيانات، اعطي خيارات
            if "❌ لا توجد بيانات" in result:
                retry_msg = (
                    "🔄 **للبحث مرة أخرى:**\n"
                    "ادخل المدة الزمنية\n\n"
                    "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                )
                await update.message.reply_text(retry_msg, parse_mode='Markdown')

                user_states[user_id] = {
                    'waiting_for': 'days_previews_count_social_media',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name')
                }
                print(f"⚠️ لا توجد بيانات للمستخدم: {user_name} - في انتظار مدة جديدة")
            else:
                options_msg = (
                    "🔄 **للبحث مرة أخرى:**\n"
                    "ادخل المدة الزمنية\n\n"
                    "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                )
                await update.message.reply_text(options_msg, parse_mode='Markdown')

                user_states[user_id] = {
                    'waiting_for': 'days_previews_count_social_media',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name')
                }
                print(f"✅ تم تنفيذ تقرير أعداد المعاينات حسب وسائل التواصل للمستخدم: {user_name}")

        # معالجة الاجتماعات بدون تصميمات
        elif waiting_for == 'branch_selection_meetings_no_designs':
            if user_input in ['1', '2', '3']:
                branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
                selected_branch = branch_names[user_input]

                await update.message.reply_text(f"✅ تم اختيار {user_input} {selected_branch}")

                user_states[user_id] = {
                    'waiting_for': 'days_meetings_no_designs',
                    'selected_branch': user_input,
                    'branch_name': selected_branch
                }

                await update.message.reply_text(
                    f"📅 من فضلك ادخل عدد ايام مده البحث المختاره او للكل اضغط # لفرع: {selected_branch}"
                )
            else:
                await update.message.reply_text("⚠️ من فضلك اختر رقم صحيح: 1 أو 2 أو 3")

        elif waiting_for == 'days_meetings_no_designs':
            if user_input == '#':
                days_value = None
            else:
                try:
                    days_value = int(user_input)
                except ValueError:
                    await update.message.reply_text("⚠️ من فضلك أدخل رقم صحيح أو # للكل")
                    return

            branch_id = user_state.get('selected_branch', '1')

            await update.message.reply_text("🔄 جاري تحضير إحصائيات الاجتماعات بدون تصميمات...")

            # الحصول على الأعداد أولاً
            result, count = get_customers_with_meetings_no_designs_count(branch_id, days_value)

            if isinstance(result, str) and count == 0:
                await update.message.reply_text(result)
                retry_msg = (
                    "🔄 **للبحث مرة أخرى:**\n"
                    "ادخل المدة الزمنية\n\n"
                    "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                )
                await update.message.reply_text(retry_msg, parse_mode='Markdown')

                user_states[user_id] = {
                    'waiting_for': 'days_meetings_no_designs',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name')
                }
                print(f"⚠️ لا توجد بيانات للمستخدم: {user_name} - في انتظار مدة جديدة")
            else:
                # عرض الإحصائيات
                await send_long_message(update, result, parse_mode='Markdown')

                # السؤال عن التفاصيل
                from telegram import ReplyKeyboardMarkup, KeyboardButton
                keyboard = [
                    [KeyboardButton("نعم - عرض التفاصيل"), KeyboardButton("لا - العودة للقائمة")]
                ]
                reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=True, resize_keyboard=True)

                details_msg = f"📋 **هل تريد عرض تفاصيل العملاء ببياناتهم؟**\n\n📊 **العدد:** {count} عميل"
                await update.message.reply_text(details_msg, parse_mode='Markdown', reply_markup=reply_markup)

                # حفظ البيانات لاستخدامها في التفاصيل
                user_states[user_id] = {
                    'waiting_for': 'details_confirmation_meetings_no_designs',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name'),
                    'days_value': days_value,
                    'count': count
                }
                print(f"✅ تم عرض إحصائيات الاجتماعات بدون تصميمات للمستخدم: {user_name}")

        elif waiting_for == 'details_confirmation_meetings_no_designs':
            if user_input == "نعم - عرض التفاصيل":
                branch_id = user_state.get('selected_branch', '1')
                days_value = user_state.get('days_value')

                await update.message.reply_text("🔄 جاري تحضير تفاصيل العملاء...")

                try:
                    # الحصول على التفاصيل
                    result = get_customers_with_meetings_no_designs(branch_id, days_value)
                    print(f"📋 نتيجة تفاصيل الاجتماعات: {len(result)} حرف")

                    if result and not result.startswith("❌"):
                        await send_long_message(update, result)
                        print(f"✅ تم إرسال تفاصيل الاجتماعات بنجاح للمستخدم: {user_name}")
                    else:
                        await update.message.reply_text(result or "❌ لا توجد تفاصيل متاحة")
                        print(f"⚠️ لا توجد تفاصيل الاجتماعات للمستخدم: {user_name}")

                except Exception as e:
                    error_msg = f"❌ حدث خطأ أثناء تحضير التفاصيل: {str(e)}"
                    await update.message.reply_text(error_msg)
                    print(f"❌ خطأ في تفاصيل الاجتماعات: {str(e)}")

                # خيارات بعد عرض التفاصيل
                options_msg = (
                    "🔄 **للبحث مرة أخرى:**\n"
                    "ادخل المدة الزمنية\n\n"
                    "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                )
                await update.message.reply_text(options_msg, parse_mode='Markdown')

                user_states[user_id] = {
                    'waiting_for': 'days_meetings_no_designs',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name')
                }
                print(f"✅ تم عرض تفاصيل الاجتماعات بدون تصميمات للمستخدم: {user_name}")

            elif user_input == "لا - العودة للقائمة":
                # العودة للقائمة الرئيسية
                user_states[user_id] = {'waiting_for': None}
                await start(update, context)
                print(f"🔙 عودة للقائمة الرئيسية للمستخدم: {user_name}")
            else:
                await update.message.reply_text("⚠️ من فضلك اختر أحد الخيارين المتاحين")

        # معالجة التصميمات بدون عقود
        elif waiting_for == 'branch_selection_designs_no_contracts':
            if user_input in ['1', '2', '3']:
                branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
                selected_branch = branch_names[user_input]

                await update.message.reply_text(f"✅ تم اختيار {user_input} {selected_branch}")

                user_states[user_id] = {
                    'waiting_for': 'days_designs_no_contracts',
                    'selected_branch': user_input,
                    'branch_name': selected_branch
                }

                await update.message.reply_text(
                    f"📅 من فضلك ادخل عدد ايام مده البحث المختاره او للكل اضغط # لفرع: {selected_branch}"
                )
            else:
                await update.message.reply_text("⚠️ من فضلك اختر رقم صحيح: 1 أو 2 أو 3")

        elif waiting_for == 'days_designs_no_contracts':
            if user_input == '#':
                days_value = None
            else:
                try:
                    days_value = int(user_input)
                except ValueError:
                    await update.message.reply_text("⚠️ من فضلك أدخل رقم صحيح أو # للكل")
                    return

            branch_id = user_state.get('selected_branch', '1')

            await update.message.reply_text("🔄 جاري تحضير إحصائيات التصميمات بدون عقود...")

            # الحصول على الأعداد أولاً
            result, count = get_customers_with_designs_no_contracts_count(branch_id, days_value)

            if isinstance(result, str) and count == 0:
                await update.message.reply_text(result)
                retry_msg = (
                    "🔄 **للبحث مرة أخرى:**\n"
                    "ادخل المدة الزمنية\n\n"
                    "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                )
                await update.message.reply_text(retry_msg, parse_mode='Markdown')

                user_states[user_id] = {
                    'waiting_for': 'days_designs_no_contracts',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name')
                }
                print(f"⚠️ لا توجد بيانات للمستخدم: {user_name} - في انتظار مدة جديدة")
            else:
                # عرض الإحصائيات
                await send_long_message(update, result, parse_mode='Markdown')

                # السؤال عن التفاصيل
                from telegram import ReplyKeyboardMarkup, KeyboardButton
                keyboard = [
                    [KeyboardButton("نعم - عرض التفاصيل"), KeyboardButton("لا - العودة للقائمة")]
                ]
                reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=True, resize_keyboard=True)

                details_msg = f"📋 **هل تريد عرض تفاصيل العملاء ببياناتهم؟**\n\n📊 **العدد:** {count} عميل"
                await update.message.reply_text(details_msg, parse_mode='Markdown', reply_markup=reply_markup)

                # حفظ البيانات لاستخدامها في التفاصيل
                user_states[user_id] = {
                    'waiting_for': 'details_confirmation_designs_no_contracts',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name'),
                    'days_value': days_value,
                    'count': count
                }
                print(f"✅ تم عرض إحصائيات التصميمات بدون عقود للمستخدم: {user_name}")

        elif waiting_for == 'details_confirmation_designs_no_contracts':
            if user_input == "نعم - عرض التفاصيل":
                branch_id = user_state.get('selected_branch', '1')
                days_value = user_state.get('days_value')

                await update.message.reply_text("🔄 جاري تحضير تفاصيل العملاء...")

                try:
                    # الحصول على التفاصيل
                    result = get_customers_with_designs_no_contracts(branch_id, days_value)
                    print(f"📋 نتيجة تفاصيل التصميمات: {len(result)} حرف")

                    if result and not result.startswith("❌"):
                        await send_long_message(update, result)
                        print(f"✅ تم إرسال تفاصيل التصميمات بنجاح للمستخدم: {user_name}")
                    else:
                        await update.message.reply_text(result or "❌ لا توجد تفاصيل متاحة")
                        print(f"⚠️ لا توجد تفاصيل التصميمات للمستخدم: {user_name}")

                except Exception as e:
                    error_msg = f"❌ حدث خطأ أثناء تحضير التفاصيل: {str(e)}"
                    await update.message.reply_text(error_msg)
                    print(f"❌ خطأ في تفاصيل التصميمات: {str(e)}")

                # خيارات بعد عرض التفاصيل
                options_msg = (
                    "🔄 **للبحث مرة أخرى:**\n"
                    "ادخل المدة الزمنية\n\n"
                    "🔙 **للعودة للقائمة الرئيسية:** اضغط /start"
                )
                await update.message.reply_text(options_msg, parse_mode='Markdown')

                user_states[user_id] = {
                    'waiting_for': 'days_designs_no_contracts',
                    'selected_branch': user_state.get('selected_branch'),
                    'branch_name': user_state.get('branch_name')
                }
                print(f"✅ تم عرض تفاصيل التصميمات بدون عقود للمستخدم: {user_name}")

            elif user_input == "لا - العودة للقائمة":
                # العودة للقائمة الرئيسية
                user_states[user_id] = {'waiting_for': None}
                await start(update, context)
                print(f"🔙 عودة للقائمة الرئيسية للمستخدم: {user_name}")
            else:
                await update.message.reply_text("⚠️ من فضلك اختر أحد الخيارين المتاحين")

    # إنشاء التطبيق
    print("🔧 إنشاء تطبيق التليجرام...")
    app = Application.builder().token(BOT_TOKEN).build()
    print("✅ تم إنشاء التطبيق بنجاح")

    # إضافة المعالجات
    print("📋 إضافة معالجات الأوامر...")
    app.add_handler(CommandHandler("start", start))
    app.add_handler(CommandHandler("test", test_db))

    # إضافة دالة العملاء بمعاينات متعددة
    async def multiple_previews(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض العملاء الذين لديهم أكثر من معاينة"""
        if not update.effective_user or not update.message:
            return

        user_name = update.effective_user.first_name or "مستخدم"
        print(f"📨 طلب العملاء بمعاينات متعددة من: {user_name}")

        try:
            await update.message.reply_text("🔄 جاري تحضير تقرير العملاء بمعاينات متعددة...")

            # الحصول على التقرير
            report = get_customers_with_multiple_previews()
            await send_long_message(update, report)
            print(f"✅ تم إرسال تقرير المعاينات المتعددة للمستخدم: {user_name}")

        except Exception as e:
            print(f"❌ خطأ في إرسال تقرير المعاينات المتعددة: {str(e)}")
            await update.message.reply_text("❌ حدث خطأ في تحضير التقرير")

    app.add_handler(CommandHandler("multiple_previews", multiple_previews))

    # معالجات الأزرار
    app.add_handler(MessageHandler(filters.Regex("^🔍 بحث عميل$"), search_customer_start))
    app.add_handler(MessageHandler(filters.Regex("^📊 استعلام عن العملاء$"), customers_inquiry_start))
    app.add_handler(MessageHandler(filters.Regex("^🌍 عدد العملاء حسب المناطق$"), regions_inquiry_start))
    app.add_handler(MessageHandler(filters.Regex("^📱 عدد عملاء وسائل التواصل$"), social_media_inquiry_start))
    app.add_handler(MessageHandler(filters.Regex("^📊 عملاء بدون معاينات$"), customers_without_previews_start))
    app.add_handler(MessageHandler(filters.Regex("^👁️ معاينات بدون اجتماعات$"), previews_without_meetings_start))
    app.add_handler(MessageHandler(filters.Regex("^🤝 اجتماعات بدون تصميمات$"), meetings_without_designs_start))
    app.add_handler(MessageHandler(filters.Regex("^🎨 تصميمات بدون عقود$"), designs_without_contracts_start))
    app.add_handler(MessageHandler(filters.Regex("^👁️ أعداد معاينات المناطق$"), previews_count_regions_start))
    app.add_handler(MessageHandler(filters.Regex("^📱 أعداد معاينات وسائل التواصل$"), previews_count_social_media_start))

    # معالج النصوص
    app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_text))
    
    print("✅ البوت جاهز للعمل!")
    print("📱 أرسل /start في التليجرام")
    print("🧪 أرسل /test لاختبار قاعدة البيانات")
    print("⏹️ اضغط Ctrl+C لإيقاف البوت")
    print("=" * 50)
    
    # تشغيل البوت
    print("🚀 بدء تشغيل TeERA BOT...")
    print("✅ البوت جاهز ويعمل الآن!")
    print("📱 اكتب /start في التليجرام للبدء")
    print("🧪 اكتب /test لاختبار قاعدة البيانات")
    print("⏹️ اضغط Ctrl+C لإيقاف البوت")
    print("=" * 50)
    try:
        app.run_polling(drop_pending_updates=True)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البوت")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")

if __name__ == "__main__":
    print("🚀 بدء تشغيل TeERA BOT...")
    print("📱 البوت جاهز لاستقبال الرسائل في التليجرام")
    print("🔗 Token: 7556046481:AAEMttgLYzrMu1q2ogRlgsV8mgscqZKbbcY")
    print("💾 قاعدة البيانات: alisamaraa.ddns.net,4100 - Terra")
    print("⚡ اكتب /start في البوت للبدء")
    print("=" * 50)
    main()
